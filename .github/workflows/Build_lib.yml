name: Library Build Bookings

on:
  workflow_call:
    inputs:
      CLIENT:
        description: 'customization folder name'
        type: string
        required: true
      CODE_MODE:
        description: 'Code runtime configuration'
        type: string
        required: true
        default: 'build:dev'
      DEPENDENCIES_MODE:
        description: 'npm install mode (strict use of package-lock.json)'
        type: string
        required: true
        default: 'ci'
      UPLOAD_TO_AWS:
        description: 'Upload to AWS?'
        type: string
        required: true
        default: 'false'
      AWS_FOLDER: 
        description: 'Destination folder in AWS bucket'
        required: false
        default: ""
        type: string
      AWS_AUTH_REGION:
        description: ''
        required: false
        default: "eu-south-2"
        type: string
      FAIL_IF_IN_BLACKLIST:
        description: "Fails if the client UUID in the app.plist seems unintentional"
        required: false
        default: 'yes'
        type: string
    secrets:
      PAT_ROBOT_CI:
        description: 'PAT token'
        required: true
      AWS_KEY_ID:
        description: 'AWS credentials'
        required: true
      AWS_SECRET_ACCESS_KEY:
        description: 'AWS credentials'
        required: true

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        repository: miolabs/DLOrderWebLib
        token: ${{ secrets.PAT_ROBOT_CI }}
        ref: "main"
        path: "Libs/DLOrderWebLib"

    - uses: actions/checkout@v4
      with:
        path: "Apps/DLOrderWeb"

    - name: Install xmllint
      run: sudo apt-get update && sudo apt-get install -y libxml2-utils

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install lxml

    - name: Compare client with well-known UUIDs
      id: client_uuid
      run: |
        cd Apps/DLOrderWeb
        PWD=`pwd`
        BASE="${PWD}/customizations/${{ inputs.CLIENT }}"
        xml_file="$BASE/app.plist"
        online_scheme_id=$(xmllint --xpath "string(//dict/key[text()='OnlineSchemeID']/following-sibling::string[1])" "$xml_file")
        online_scheme_id=$(echo "$online_scheme_id" | tr '[:lower:]' '[:upper:]')
        CLIENT_UUID="$online_scheme_id"
        INVALID_VALUES=(
          "3EE79CA8-5595-4086-A0C3-2AADF7A59B70"
          )
        MATCH_FOUND=0
        for VALUE in "${INVALID_VALUES[@]}"; do
          if [[ "$CLIENT_UUID" == "$VALUE" ]]; then
            MATCH_FOUND=1
            break
          fi
        done
        if [[ "$MATCH_FOUND" == "1" && "${{ inputs.FAIL_IF_IN_BLACKLIST }}" == "yes" ]]; then
          echo "Client UUID in app.plist is in the black list and FAIL_IF_IN_BLACKLIST is true"
          exit 1
        fi
        echo "uppercase_client_uuid=$online_scheme_id" >> $GITHUB_OUTPUT
        
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: 18.x

    - name: Instalar tree
      run: sudo apt-get update && sudo apt-get install -y tree

    - name: Call Auth API to get server URLs
      id: server_urls
      run: |
        cd Apps/DLOrderWeb
        PWD=`pwd`
        BASE=${PWD}/customizations/${{ inputs.CLIENT }}
        client_uuid="${{ steps.client_uuid.outputs.uppercase_client_uuid }}"
        response=$(curl -X GET https://auth.dual-link.com/business/$client_uuid)
        apiURL=$(echo "$response" | jq -r '.data.urls.APIServerURL')
        echo "El valor  de apiURL es: $apiURL"
        echo "API_URL=$apiURL" >> $GITHUB_ENV
        syncURL=$(echo "$response" | jq -r '.data.urls.SyncServerURL')
        echo "El valor  de syncURL es: $syncURL"
        echo "SYNC_URL=$syncURL" >> $GITHUB_ENV
        paymentURL=$(echo "$response" | jq -r '.data.urls.PaymentServerURL')
        echo "El valor  de paymentURL es: $paymentURL"
        echo "PAYMENT_URL=$paymentURL" >> $GITHUB_ENV
        onlineURL=$(echo "$response" | jq -r '.data.urls.OnlineServerURL')
        echo "El valor  de onlineURL es: $onlineURL"
        echo "ONLINE_URL=$onlineURL" >> $GITHUB_ENV
  
    - name: Copy resources
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        CUSTOM="${PWD}/customizations/${CLIENT}"
        OUTPUT="${PWD}/customizations/${CLIENT}/deploy"
        echo " Creating distribution for customer:   '${CLIENT}'"
        echo " Source of customized files:           '${CUSTOM}'"
        echo " Destination:                          '${OUTPUT}'"
        # ------ remove and rebuild deploy directory
        echo " Creating output folders"
        if [ -d "${OUTPUT}" ]; then rm -rf "${OUTPUT}"; fi
        mkdir -p "${OUTPUT}"
        mkdir -p "${OUTPUT}/dlorderweblib"
        mkdir -p "${OUTPUT}/libs/miojslibs"
        mkdir -p "${OUTPUT}/libs/miojslibs/webworkers"
        # ------ Copy files
        echo " Copying files"
        cp -r ./resources/libs ./resources/main.js ./resources/index.php ./resources/app.plist ./resources/bundles.json ./resources/DLOrderWebLib/css/iconfont.css "${OUTPUT}"
        rm -rf ./resources/DLOrderWebLib/css/_theme.scss
        cp -r ./resources/DLOrderWebLib/* "${OUTPUT}/dlorderweblib/"
        cp ./sources/DLOrderWebLib/model/datamodel.xml "${OUTPUT}/dlorderweblib/"
        cp -r ./resources/DLOrderWebLib/languages "${OUTPUT}/languages"
        cp -r ./resources/animate.min.css "${OUTPUT}"
        php -d include_path="${OUTPUT}"  "${OUTPUT}/index.php" > "${OUTPUT}/index.html"
        rm "${OUTPUT}/index.php"
        cp "${CUSTOM}/app.plist" "${OUTPUT}"
        cp -rf "${CUSTOM}/images" "${CUSTOM}/fonts" "${CUSTOM}/templates" "${OUTPUT}"
        if [ -d "${CUSTOM}/scripts" ]; then cp -rf "${CUSTOM}/scripts" "${OUTPUT}"; fi
        cp -rf "${CUSTOM}/layout" "${OUTPUT}/dlorderweblib/"
        #rm -rf ./resources/DLOrderWebLib/css/_theme.scss
        #ln -s "${CUSTOM}/theme.scss" ./resources/DLOrderWebLib/css/_theme.scss
        cp -f "${CUSTOM}/theme.scss" ./resources/DLOrderWebLib/css/_theme.scss
        tree "${PWD}/customizations/${CLIENT}"

    - name: Insert server urls in app.plist
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        OUTPUT="${PWD}/customizations/${CLIENT}/deploy"
        xml_file="${OUTPUT}/app.plist"
        export APP_PLIST_FILE="$xml_file"
        python3 ./utils/insert_server_urls.py
        xmllint --format $xml_file -o $xml_file
        cat $xml_file

    - name: Compile Styles
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        CUSTOM="${PWD}/customizations/${CLIENT}"
        OUTPUT="${PWD}/customizations/${CLIENT}/deploy"
        npx sass -I. ./resources/DLOrderWebLib/css/styles.scss "${OUTPUT}/app.css"
      
    - name: Compile ts
      continue-on-error: true
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        CUSTOM="${PWD}/customizations/${CLIENT}"
        OUTPUT="${PWD}/customizations/${CLIENT}/deploy"
        #npm ${{ inputs.DEPENDENCIES_MODE }}
        npm install
        npx tsc --outFile "${OUTPUT}/app.js"

    - name: Result
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        tree "${PWD}/customizations/${CLIENT}"
        cat "${PWD}/customizations/${CLIENT}/deploy/app.plist"

    - name: AWS Authentication
      if: ${{ inputs.UPLOAD_TO_AWS == 'true' }}
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ inputs.AWS_AUTH_REGION }}

    # Get the OnlineSchemeID from app.plist in uppercase
    - name: Calculate AWS folder
      id: get_folder
      run: |
        cd Apps/DLOrderWeb
        if [ -z "${{ inputs.AWS_FOLDER }}" ]; then
          CLIENT=${{ inputs.CLIENT }}
          PWD=`pwd`
          CUSTOM="${PWD}/customizations/${CLIENT}"
          xml_file="${CUSTOM}/app.plist"
          online_scheme_id=$(xmllint --xpath "string(//dict/key[text()='OnlineSchemeID']/following-sibling::string[1])" "$xml_file")
          echo "OnlineSchemeID: $online_scheme_id"
          folder="$online_scheme_id"
        else
          folder="${{ inputs.AWS_FOLDER }}"
        fi
        folder=$(echo "$folder" | tr '[:lower:]' '[:upper:]')
        echo "folder=$folder" >> $GITHUB_OUTPUT
        echo "Destination folder: $folder"

    - name: Upload to S3
      if: ${{ inputs.UPLOAD_TO_AWS == 'true' }}
      run: |
        cd Apps/DLOrderWeb
        CLIENT=${{ inputs.CLIENT }}
        PWD=`pwd`
        CUSTOM="${PWD}/customizations/${CLIENT}"
        cd "$CUSTOM"
        AWS_FOLDER=${{ steps.get_folder.outputs.folder }}
        echo "AWS Destination folder: $AWS_FOLDER"
        if [ -n "$AWS_FOLDER" ]; then
          aws s3 cp deploy/ s3://webapps-dl/$AWS_FOLDER/ --recursive
        else
          echo "AWS_FOLDER empty!! No upload to S3 will be performed"
          exit 5
        fi

    - name: Invalidating AWS cloudfront cache
      if: ${{ inputs.UPLOAD_TO_AWS == 'true' }}
      run: |
        AWS_FOLDER=${{ steps.get_folder.outputs.folder }}
        echo "AWS Destination folder: $AWS_FOLDER"
        if [ -n "$AWS_FOLDER" ]; then
          echo "Invalidating $AWS_FOLDER"
          aws cloudfront create-invalidation --distribution-id E26HHG8KHHGL37 --paths "/$AWS_FOLDER/*"
        else
          echo "AWS_FOLDER empty!! No action will be performed"
        fi
