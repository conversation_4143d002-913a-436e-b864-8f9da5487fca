name: Invalidate AWS Cache

# AWS CloudFront caches the files so modifications can take days to propagate. Even deleting and recreating the bucket does not help

on:
  workflow_dispatch:
    inputs:
      CLIENT_UUID:
        description: 'Client UUID (bucket folder to invalidate recursively)'
        required: true

jobs:
  Invalidate:
    runs-on: ubuntu-latest
    steps:
     - name: AWS Authentication
       uses: aws-actions/configure-aws-credentials@v4
       with:
         aws-access-key-id: ${{ secrets.AWS_KEY_ID }}
         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
         aws-region: eu-south-2

     - name: Invalidating
       run: |
         echo "Invalidating ${{ github.event.inputs.CLIENT_UUID }}"
         aws cloudfront create-invalidation --distribution-id E26HHG8KHHGL37 --paths "/${{ github.event.inputs.CLIENT_UUID }}/*"
