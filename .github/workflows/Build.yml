name: <PERSON><PERSON>rder<PERSON>eb Build

on:
  workflow_dispatch:
    inputs:
      CLIENT:
        description: 'customization folder name'
        required: true
      CODE_MODE:
        description: 'Code runtime configuration'
        required: true
        type: choice
        options:
          - 'build:dev'
          - 'build:prod'
        default: 'build:dev'
      UPLOAD_TO_AWS:
        description: 'Upload to AWS?'
        required: true
        type: choice
        options:
          - 'false'
          - 'true'
        default: 'false'
      AWS_FOLDER:
        description: 'Destination folder in AWS. Leave blank por client UUID. Note that it will be converted to uppercase'
        required: false
        default: ""
      FAIL_IF_IN_BLACKLIST:
        description: 'Fails if the client UUID in app.plist seems unintentional'
        required: false
        type: choice
        options:
          - 'yes'
          - 'no'
        default: 'yes'

jobs:
  call-build:
    uses: miolabs/DLOrderWeb/.github/workflows/Build_lib.yml@master
    with:
      CLIENT: ${{ github.event.inputs.CLIENT }}
      CODE_MODE: ${{ github.event.inputs.CODE_MODE }}
      DEPENDENCIES_MODE: "install"
      UPLOAD_TO_AWS: ${{ github.event.inputs.UPLOAD_TO_AWS }}
      AWS_FOLDER: ${{ github.event.inputs.AWS_FOLDER }}
      FAIL_IF_IN_BLACKLIST: ${{ github.event.inputs.FAIL_IF_IN_BLACKLIST }}
    secrets:
      PAT_ROBOT_CI: ${{ secrets.PAT_ROBOT_CI }}
      AWS_KEY_ID: ${{ secrets.AWS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
         
