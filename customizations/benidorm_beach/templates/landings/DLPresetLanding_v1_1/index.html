<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Benidorm Beach | Dual Link Digital Menu</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <link href="app.css" rel="stylesheet" />
</head>
<body>
    <div id="digital-menu-preset-selection-view" class="desktop">

        <div class="bg-video-container">
            
            <!----------------->
            <!-- The video BG-->
            <video id="bg-video" autoplay muted loop>
                <source src="video/benidorm.webm" type="video/webm">
            </video>

            <div class="screen"></div>

            <!-- Optional: some overlay text to describe the video
            <div class="content">
                <h1>Heading</h1>
                <p>Lorem ipsum...</p>

                //Use a button to pause/play the video with JavaScript 
                <button id="myBtn" onclick="toggleVideoPlay()">Pause</button>
            </div> -->

        </div>

        <!------------------------------------------------------------>
        <!--SLIDE IN WINDOW with category list (absolute positioned)-->
        <div id="nav-window" class="nav-window bg-panel">

            <!--click this-->
            <div id="close-menu-btn" class="hamburger align-self-end" data-outlet="hamburger-close-btn">
                <div class="main-nav-toggle active-menu">
                <i>x close</i>
                </div>
            </div>

            <!--nav items-->
            <ul class="align-self-end">
                <li><a href="#item1" class="icon pinocchio"></a>
                    <div class="title">Pinocchio</div>
                </li>
                <li><a href="#item2" class="icon dual-link "></a>
                    <div class="title">Dual Link</div>
                </li>
                <li><a href="#item3" class="icon laola"></a>
                    <div class="title">La Ola Burgers & Grill</div>
                </li>
                <li><a href="#item4" class="icon solysombra"></a>
                    <div class="title">Sol y Sombra Beach Bar</div>
                </li>
            </ul>

            <!--back to top btn-->
            <div class="to-top-btn oobtn oobtn-secondary align-self-end" id="scroll-top-btn">Back to top</div>
          
        </div>

        <!---------------------->
        <!--TOP BAR NAVIGATION-->
        <header class="nav-bar-wrapper">
          <div id="nav-bar" class="nav-bar fade-in fast">
            
            <!--click this-->
            <div id="open-menu-btn" class="hamburger justify-self-start" data-outlet="hamburger-btn">
              <div class="main-nav-toggle">
                <i>Menu</i>
              </div>
            </div>
            
            <!--LOGO-->
            <div id="logo-container" class="logo-container"></div>
            
            <!--ICON (goes back to PresetSelectView)-->
            <a class="back-btn justify-self-end" href="https://webapps.dual-link.com/CA10E50B-8070-4C25-9F02-66B4B3D557B7/home/"></a>
  
          </div>
        </header>

        <!-- <header>
            <div class="logo" aria-label="logo"></div>
            <p class="sub-text hide-on-mobile" style="text-align: center;">
                Minimum spend policy (Excluding shisha)  •  MON-THU: AED 120/person  •  FRI-SUN: AED 170/person
            </p>
            <p class="sub-text hide-on-desktop" style="text-align: center; line-height: 1.3em;">
                Minimum spend policy (Excluding shisha)<br />
                MON-THU: AED 120/person<br />
                FRI-SUN: AED 170/person
            </p>
        </header> -->



        <!-------------------------------->
        <!--Holds all elements on screen-->
        <div class="content-container">

            <div class="hero-container align-items-center">
                <h1 class="text-align-center">SCAN, CHOOSE, ENJOY.</h1>
                <p>Place your order from your sunbed in seconds.</p>
            </div>

            <div id="tramo-1" class="section-info-container bg-panel">
                <h1>Section 1 Benidorm Beach</h1>
                <p class="">Check out these cool places to eat!</p>
            </div>

            <div id="item1" class="venue-info-container bg-panel">
                <a class="icon pinocchio row-span-3" target="_blank" href="https://webapps.dual-link.com/E8B0D065-D704-4DB1-B696-2C920FC62B30/?type=menu&menu-preset=A75B2C0D-9FB4-461A-9A93-59BF906EA51A"></a>
                <h1>Pinocchio</h1>
                <p>Un lugar vibrante y acogedor donde los helados artesanales, sabores audaces y coberturas creativas se combinan para ofrecer una experiencia dulce para todas las edades.</p>
                <a target="_blank" href="#"><span>Pedir del restaurante aquí</span></a>
            </div>

            <div id="item2" class="venue-info-container bg-panel">
                <a class="icon dual-link row-span-3" target="_blank" href="https://webapps.dual-link.com/E8B0D065-D704-4DB1-B696-2C920FC62B30/?type=menu&menu-preset=3EE79CA8-5595-4086-A0C3-2AADF7A59B70"></a>
                <h1>Dual Link Gourmet Bistro</h1>
                <p>Un destino culinario elegante que ofrece platos meticulosamente elaborados, ingredientes de temporada y una experiencia gastronómica refinada que combina la innovación con técnicas clásicas.</p>
                <a target="_blank" href="#"><span>Pedir del restaurante aquí</span></a>
            </div>

            <div id="item3" class="venue-info-container bg-panel">
                <a class="icon laola row-span-3" target="_blank" href=""></a>
                <h1>La Ola Burgers & Grill</h1>
                <p>Las mejores hamburguesas gourmet de la playa, acompañadas de patatas crujientes y bebidas heladas. Perfecto para un almuerzo relajado con vistas al Mediterráneo.</p>
                <a target="_blank" href="#"><span>Pedir del restaurante aquí</span></a>
            </div>

            <div class="venue-info-container bg-panel">
                <a id="item4" class="icon solysombra row-span-3" target="_blank" href=""></a>
                <h1>Sol y Sombra Beach Bar</h1>
                <p>Disfruta de cócteles artesanales, zumos naturales y tapas frescas sin moverte de la hamaca. Servicio rápido y sabores mediterráneos junto al mar.</p>
                <a target="_blank" href="#"><span>Pedir del restaurante aquí</span></a>
            </div>

            <!-- <div class="preset-grid justify-items-center"> -->

                
                <!-- 
                <div class="venue-info-container bg-panel">
                    <a id="item5" class="icon lgymat row-span-2" target="_blank" href="https://webapps.dual-link.com/CA10E50B-8070-4C25-9F02-66B4B3D557B7/?type=menu&menu-preset=f3582e3c-5910-47d5-8bb5-92c26b0fe2c2"></a>
                    <h1>Restaurant Name</h1>
                    <p>Some description about the restaurant here, check it out.</p>
                </div>

                <div class="venue-info-container bg-panel">
                    <a id="item6" class="icon glaze row-span-2" target="_blank" href="https://webapps.dual-link.com/CA10E50B-8070-4C25-9F02-66B4B3D557B7/?type=menu&menu-preset=3d78126b-fca6-4496-82ad-3c447c55ca1f"></a>
                    <h1>Restaurant Name</h1>
                    <p>Some description about the restaurant here, check it out.</p>
                </div>

                <div class="venue-info-container bg-panel">
                    <a id="item7" class="icon treats row-span-2" target="_blank" href="https://webapps.dual-link.com/CA10E50B-8070-4C25-9F02-66B4B3D557B7/?type=menu&menu-preset=4195b7b0-3394-43e8-9a48-3d92ddfb1a8b"></a>
                    <h1>Restaurant Name</h1>
                    <p>Some description about the restaurant here, check it out.</p>
                </div> -->

            <!-- </div> -->

 


        </div><!--end of content-grid-container-->


        <footer class="footer">
            <div class="footer-content-container justify-space-between">

                <div class="footer-box">
                    <img class="sponsor-logo" src="images/logo-dual-link.png" alt="">
                </div>

                <div class="footer-box">
                    <img class="sponsor-logo" src="images/GV_GRIS.png" alt="">
                </div>
            </div>
        </footer>

         <div class="sub-footer">
            <div class="sub-footer-container justify-content-center">

                <p class="copy-right justify-items-center text-align-center">
                    © Turisme Comunitat Valenciana, 2025. Todos los derechos reservados.
                </p>


            </div>
        </div>
        

    </div><!--End Preset List View-->


    <script type="text/javascript">


        // Get window/hamburger variables
        const open_window_btn = document.getElementById("open-menu-btn");
        const close_window_btn = document.getElementById("close-menu-btn");
        const nav_window = document.getElementById("nav-window");

        open_window_btn.addEventListener('click', openWindow);
        close_window_btn.addEventListener('click', closeWindow);

        // Opens nav window by adding .show-window class
        function openWindow(){
            nav_window.classList.add("show-window");
        }

        // Closes nav window by removing .show-window class
        function closeWindow(){
            nav_window.classList.remove("show-window");
        }
        

        
        // Get scroll to top variables
        const scrollToTopBtn = document.getElementById("scroll-top-btn");
        scrollToTopBtn.addEventListener('click', scrollToTop);
        function scrollToTop(){
            window.scrollTo(0, 0);
            nav_window.classList.remove("show-window");
        }
        

        
        // Get the video and button
        const video = document.getElementById("Benidorm");
        const btn = document.getElementById("myBtn");

        // Pause and play the video, and change the button text
        function toggleVideoPlay() {
            if (video.paused) {
                video.play();
                btn.innerHTML = "Pause";
            } else {
                video.pause();
                btn.innerHTML = "Play";
            }
        }


        //header reduction On Scroll
        // const scrollObj = document.getElementById("product-table-scroller");

        // scrollObj.onscroll = function() {headerResize()};

        // function headerResize() {
        //         const logo = document.getElementById("logo-container");
        //         if (logo && scrollObj.scrollTop > 50) {
        //             MUICoreLayerAddStyle(logo, "reduce-logo");
        //         } else {
        //             MUICoreLayerRemoveStyle(logo, "reduce-logo");
        //         }
        // } 

        const logo = document.getElementById("logo-container");

        window.onscroll = () => {

            if (this.scrollY <= 50) {
                logo.classList.remove('reduce-logo');
            } else {
                logo.classList.add('reduce-logo');
            }
        };

        // ScrollTrigger.create({
        //     animation:gsap.from("#logo-container", {
        //         y:"50vh",
        //         scale:4,
        //         yPercent:-50
        //     }),
        //     scrub:true,
        //     trigger: ".content-container",
        //     start: "top bottom",
        //     endTrigger: '.content-container',
        //     end: 'top center',
        //     markers: true,
        //     pin: true,
        //     pinSpacing: false
        // });


    </script>
</body>
</html>