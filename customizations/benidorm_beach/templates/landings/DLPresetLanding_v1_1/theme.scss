
//////////////////////////////////////////////////////////////
//     http://meyerweb.com/eric/tools/css/reset/            //
//     v2.0 | 20110126                                      //
//     License: none (public domain)                        //
//////////////////////////////////////////////////////////////

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
    display: block;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before,
blockquote::before,
blockquote:after,
blockquote::after,
q:before,
q::before,
q:after,
q::after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}


// DECLARE NEW FONTS //////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.roboto-light {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 200;
  font-style: normal;
  font-variation-settings:
    "wdth" 100;
}

.roboto-regular {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings:
    "wdth" 100;
}

.roboto-medium {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings:
    "wdth" 100;
}

.roboto-bold {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  font-variation-settings:
    "wdth" 100;
}




// VARIABLES //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

$brand-color-1: #3e6c93;
$brand-color-2: #16103B;
$brand-color-3: #C7899A;
$brand-color-4: #F6ECDC;
$brand-font-1: "Roboto", sans-serif;
$brand-font-2: "Roboto", sans-serif;


$font-family: $brand-font-1;
// #00BEBE (light)   (dark)
$action-color: $brand-color-1;
$action-color-dark: $brand-color-3;
$bg1: rgba(0, 0, 0, 1); // #F9F4EE ;
$bg2: rgba(255, 255, 255, .3);
$fg1: #4b3918;
$stripe-color: rgba(255, 255, 255, .2);
$curtain_color: $bg1;

$border-color: #D0D0DB;
$text-primary-color: #222222; //#1A1A1A ;
$text-secondary-color: #545454;
$text-inverted-color: #000000;

$price-color: #77a464;

$border-radius: 8px;
$general-gap: 8px;

// BODY PROPERTIES ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

* {
    box-sizing: border-box;
}

html {
    box-sizing: border-box;
    background: none;
    min-height: 100dvh;

    font-weight: 400;
    font-family: $brand-font-1;
    font-weight: normal;
    font-style: normal;
    color: #212529;

    scroll-behavior: smooth;
}

body {
    position: relative;
    display: block;
    height: auto;
    width: 100%;
    color: inherit;
    background: none;
}

a {
    color: $action-color;
    // transition: all ease-in-out .15s;
    transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
    text-decoration: none;

    &:hover {
        filter: brightness(1.2);
    }
}

.bg-bg1 {
    background-color: transparent !important;
}

.gap {
    gap: $general-gap;
}

/// SCROLL BAR PROPERTIES ////////////// 

/* Works on Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: #888 #aaa;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 10px;
}

*::-webkit-scrollbar-track {
    background: #aaa;
}

*::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
    border: 3px solid #aaa;
}

.selected .checkbox-button::before,
.checkbox-button.selected::before,
.custom-selected .checkbox-button::before {
    border-bottom: 4px solid $action-color;
    border-right: 4px solid $action-color;

}

// GENERAL MODUALS ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.view,
.window {
    position: relative;
}

.oobtn {
    // transition: all ease-in-out .15s;
    transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);


    &.oobtn-primary {
        background-color: $action-color;
        border-radius: 4px;
        color: $text-primary-color;

        background-image: linear-gradient(to right, $action-color, $action-color-dark);
    }

    &.oobtn-secondary {
        border: 1px solid $action-color;
        border-radius: 4px;
        color: $action-color;
        font-weight: bold;

        padding-left: 25px;
        padding-right: 25px;
    }

    &:hover {
        filter: brightness(1.2);
    }
}

.btn-minus,
.btn-plus,
.oobtn-icon {
    // transition: all ease-in-out .15s;
    transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
    cursor: pointer;
    color: $action-color;
    background: transparent !important;

    &:hover {
        filter: brightness(1.2);
    }
}


input,
textarea,
.combobox,
select {

    box-sizing: border-box !important;
    // font-family: 'gil-sans-light', sans-serif !important;
    // background: rgba(0, 0, 0, .1) !important;

    // FIX: campos no se alinean bien en producción, falta width de los input
    width: 100%;

    & option {
        // background: rgba(0, 0, 0, .9);
        // color: #fff;
        // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
    }
}


#pickup-or-delivery .logo-container {
    background: url("images/logo.png") center center;
    background-repeat: no-repeat;
    background-size: contain;

    min-height: 150px;
    width: 100%;
    max-width: 350px;

    // filter: drop-shadow(2px 4px 12px #000000);
}

.logo-title-container {
    font-family: $brand-font-2;
    font-size: 140%;
    color: $text-primary-color;
}


// prefered no background as default
#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
    background: transparent;
    overflow-y: auto;
    height: 100dvh;
    width: 100%;
    box-sizing: border-box !important;
}

.modal_window {
    color: $text-primary-color;
    border: 1px solid rgba(255, 255, 255, 0.33) !important;
    background-color: rgba(0, 0, 0, .9);
    box-shadow: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22) !important;
    border-radius: 8px;
}

// .footer-m {
//   background: linear-gradient(to right bottom, rgba(0,0,0,.4),rgba(0,0,0,.3)) !important;
// }

.footer-m-embed,
.footer-m {
    height: auto;
    padding: 8px;
}

.spin-loader {
    border: 3px solid $action-color !important;
    border-right-color: transparent !important;
    width: 32px;
    height: 32px;
}

.stepper .dot.selected {
    background-color: $text-inverted-color;
}

.stepper .dot.selected::before {
    border: 2px solid $text-inverted-color;
}

.selected .radio-button::before,
.custom-selected .radio-button::before {
    background-color: $action-color;
}

.button-close {
    &.cta {
        color: $action-color !important;
        background: transparent !important;
    }
}

input:focus,
textarea:focus {
    border-color: $action-color !important;
    outline-color: $action-color !important;
    outline: 0 !important;
}

.flex-row {
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.wrap-col { flex-wrap: wrap; }

.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-full { grid-column: 1/-1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-5 { grid-row: span 5; }
.row-span-full {grid-row: 1/-1; }

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; } 
.justify-content-end { justify-content: flex-end; } 
.justify-items-start { justify-items: flex-start; } 
.justify-items-center { justify-items: center; } 
.justify-items-end { justify-items: flex-end; } 
.justify-self-start { justify-self: flex-start; } 
.justify-self-center { justify-self: center; } 
.justify-self-end { justify-self: flex-end; } 
.justify-space-between { justify-content: space-between; }
.align-content-start { align-content: flex-start; } 
.align-content-center { align-content: center; } 
.align-content-end { align-content: flex-end; } 
.align-items-start { align-items: flex-start; } 
.align-items-center { align-items: center; } 
.align-items-end { align-items: flex-end; } 
.align-self-start { align-self: flex-start; }  
.align-self-center { align-self: center; } 
.align-self-end { align-self: flex-end; }

.text-align-left { text-align: left; }
.text-align-center { text-align: center; }
.text-align-right { text-align: right; }



// TABLE PROPERTIES ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#product-table-scroller {
    scroll-behavior: smooth;
    padding-bottom: 100px;
}

.cell {
    border: none !important;
}

.hero {
    background: none;
    color: $text-primary-color;
}

.photo-tmpl .product-image {
    margin: 8px 8px 0 8px;
    border-radius: 8px !important;
    background-color: #ffffff;
    background-repeat: no-repeat;
    height: auto;
    min-height: 235px;
    background-size: 106% !important;

    border: 1px solid lightgray;
}


.mobile .product-table,
.desktop .product-table {

    display: grid !important;
    grid-template: auto / auto;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
    padding-left: 8px;
    padding-right: 8px;

    .section-header {
        background-color: $brand-color-2;
        // background-image: linear-gradient(to right, #f6c82a, #d78a1e);
        color: $action-color;
        // border-bottom: 1px solid $action-color;
        background-color: $brand-color-2;
        background: url("images/bg1.jpg") no-repeat;
        background-size: cover;
        box-shadow: 0 8px 10px rgba(0, 0, 0, 0.2), 0 6px 8px rgba(0, 0, 0, 0.3);
        
        &::after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: -1;
            background: linear-gradient(to right top, rgba(255,255,255,.15),rgba(255,255,255,.05)) !important;
            border: 1px solid #464450;
            border-radius: 4px !important;
        }
    }

    .menu-section-header,
    .menu-section-title {
        grid-column: 1 / -1 !important;
        box-sizing: border-box;
        border-radius: 0;
        color: $action-color;
        font-family: $brand-font-1;
        font-size: 14px;
        font-weight: 500;
        text-transform: uppercase;

    }

    // .menu-section-title:before,
    // .menu-section-title:after {
    //     content: "";
    //     display: block;
    //     width: 100%;
    //     height: 20px;
    //     background: url( 'images/divider.svg' ) no-repeat center center;
    //     background-size: contain
    // }

    .cell {
        width: 100% !important;
        margin: 0 !important;
        background-color: none;
        font-size: 14px;
        color: $text-primary-color;
        // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

        &>div {
            // border-radius: 1px solid $text-primary-color;
            // outline: 5px solid $bg2;
        }

        .hero {
            border-radius: 0 !important;
            margin-top: 0 !important;
            background-color: transparent !important;
            color: $text-primary-color;
        }

        // .image-cover {
        //     background-size: 105% auto !important;
        // }


    }


}

// make cells not rounded
.cell.rounded {
    // border-radius: 0 !important;
    // overflow: hidden;
}

.desktop #product-list-view>.product-table>.cell.product {
    // width: 233px !important;
    // min-width: 220px;
    // // flex: 1;
    // border-radius: 0px !important;
}




// PICKUP OR DELIVERY /////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#pickup-or-delivery {

    .pickup-icon,
    .take-away-icon,
    .table-icon {
        width: 150px;
        height: 150px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;

        border: 1px solid $brand-color-1;
        border-radius: 50%;
        padding: 16px;
        background-size: 70% !important;
        // background-image: linear-gradient(to right, #f6c82a, #5e180e);
    }

    .take-away-icon {
        background-image: url(images/take_away_dark.png) !important;
    }

    .pickup-icon {
        background-image: url(images/pickup_dark.png) !important;
    }

    .table-icon {
        background-image: url(images/table_dark.png) !important;
    }

    .take-away-block {}

    .pickup-block {}

    .table-block {}



    .grid-col {
        display: grid;
        grid-template-rows: 1fr auto;
        flex-wrap: nowrap;
    }
}



// CHOOSE MENU VIEW ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.desktop .menu-view-grid-container {
    display: grid;
    gap: 8px;
    grid-template-columns: minmax(0, 1fr) 360px;
    padding-left: 8px;
    padding-right: 8px;

    .ticket-view-wrapper {
        grid-row: 1/3;
        grid-column: 2/3;
    }
}

.mobile .menu-view-grid-container {
    display: grid;
    gap: 4px;
    grid-template-columns: minmax(0, 1fr);
    padding: 4px 4px 0 4px;
}




/////////////////// CATEGORY MENU VIEW //////////////////////////

#category-menu-view {
    position: relative !important;
    font-size: 13px;
    letter-spacing: .05em;
    text-transform: uppercase;
    font-weight: 400;
    border-radius: 0;

    .glass-panel {
        border-radius: 3px !important;
    }

    .scroll-row {
        position: relative !important;
    }

    .menu-item {

        position: relative !important;

        &:hover,
        &.selected {}
    }

    .oobtn-link {
        // color: #323232;
        font-weight: bold;
    }

    .oobtn-next,
    .oobtn-prev {
        position: relative !important;

        display: flex;
        align-items: center;

    }



    .oobtn-next:hover:before,
    .oobtn-prev:hover:before,
    .oobtn-link:hover {
        opacity: 1 !important;
        filter: brightness(1.2);
    }


    .btn-minus.disabled,
    .btn-plus.disabled,
    .oobtn-icon.disabled {
        color: #aaaaaa;
    }
}



/////////////////// PRODUCT LIST VIEW //////////////////////////

#product-add-view {
    height: auto !important;
}

.photo-tmpl.selected {
    border: 0 !important;

    &.glass-panel::after {
        background: linear-gradient(to right top, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.40)) !important;
    }
}

/////////////////// TICKET VIEW //////////////////////////

.desktop #ticket-view {
    width: 100%;
    position: relative !important;
    top: inherit !important;
}

.ticket-view-title {
    color: $action-color;
    font-weight: bold;
}

#checkout-date-picker-view {
    height: auto !important;
}

.custom-stripe>.stripped:nth-child(2n),
.stripe>div:nth-child(2n) {
    background-color: $stripe-color;
}


// CHECK OUT VIEW /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

#checkout-view.desktop .main-view-wrapper {
    padding-right: 8px;
}


#client-info-view,
#conditions-container,
#terms-wrapper {
    background-color: transparent !important;
    border-radius: 0px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
    margin-top: 15px;
    color: $text-primary-color;

    .cell .hero {
        background-color: transparent !important;
        border-radius: 0 !important;
    }
}

.tab.selected {
    border-color: $action-color;
    color: $action-color;
}

#terms-wrapper {
    margin-left: 10px !important;
    margin-right: 10px !important;
    background-color: rgba(0, 0, 0, 0.7);
    border: 0;
    margin-bottom: 15px;
}

#payment-modal-view {}



// DIGITAL MENU ///////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


// prefered no background as default
#digital-menu-view {

    background: transparent;
    overflow-y: auto;
    height: 100dvh;
    width: 100%;
    box-sizing: border-box !important;

    // &.root-screen {
    //   max-width: 1280px;
    //   margin: 0 auto;
    // }

    .scroll-row {
        position: relative !important;
        overflow: auto;
    }

    .menu-view-grid-container {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        gap: 0;
        padding: 0;
    }

    #rest_topbar {
        position: relative;
        background-color: #252525;
        border: 0;
        flex-direction: column;
        padding-top: 8px;
        gap: 8px;

        .logo-container {
            min-height: 30px;
        }

        .logo-title-container {
            font-family: $brand-font-2;
            font-size: 140%;
            color: $text-primary-color;
        }
    }

    .logo-container {
      background: url("images/logo.png") center center;
      background-repeat: no-repeat;
      background-size: contain;

      min-height: 120px;
      width: 100%;
      max-width: 350px;

      // filter: drop-shadow(2px 4px 12px #000000);
    }

    .main-view-wrapper {
      position: relative;
      display: block;
    }

    .category-wrapper {
      display: flex;
      margin-top: 32px;
      justify-content: flex-end;
    }

    #digital-menu-category-list-view {
      display: flex;
    }

    .search-bar {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      background-color: transparent;
      box-sizing: border-box;
      padding: 8px;
      margin: 0 auto;
      max-width: 800px;

      input {
        position: relative;
        border-radius: 100px;
        -webkit-border-radius: 100px;
        -moz-border-radius: 100px;
        width: 100%;
        height: auto;
        // background-color: transparent;
        color: $text-primary-color;
        font-family: $brand-font-1 !important;
        font-size: 14px;
        font-weight: 400;
        background-color: transparent;
        border: 1px solid $text-primary-color;
        box-sizing: border-box;
        text-overflow: ellipsis;
        outline: none;
        -webkit-appearance: none;

        // &:before {
        //     content: "\e8bd";
        //     /* this is your text. You can also use UTF-8 character codes as I do here */
        //     font-family: 'feather';
        //     left: -5px;
        //     position: absolute;
        //     top: 0;
        // }
      }

      input:not(:placeholder-shown) ~ i {
        display: none;
      }
      
      // input:placeholder-shown {
      //   border-color: red;
      // }

      i {
          position: absolute;
          transform: translate(0, -50%);
          right: 16px;
          top: 50%;
      }
    }

    #digital-menu-product-list-view {
        position: relative !important;
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 100%;
        padding: 0 !important;

        max-width: 800px;
        min-height: 250px;
        margin: 0 auto;

        .spin-loader-container {
            position: absolute;
            display: block;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translatey(-50%);
        }

        .uitable {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            width: 100%;
            //give some space after last cell
            padding-bottom: 32px;
            padding-top: 8px;

            .cell {
                position: relative;
                display: grid;
                grid-template-columns: 1fr;
                gap: 16px;
                height: auto;
                padding: 8px;

                .grid-row {
                    display: grid;
                    gap: 16px;
                    grid-template-columns: auto 1fr auto;
                }
            
                .grid-col {
                    display: grid;
                    grid-template-rows: auto auto minmax(0, 1fr);
                }

                //makes product image occupy the three left rows
                .product-image-row-span {
                    grid-row: 1 / 3;
                }

                .label.product-title {
                    font-family: $brand-font-2;
                    font-size: 2em;
                    // letter-spacing: -.05em;

                    text-transform: lowercase;

                    &:first-letter {
                        text-transform: uppercase;
                    }

                    .p-regular {
                        // letter-spacing: -.05em;
                        line-height: 1.25;
                    }
                }
            }

            .menu-section-title {
                padding: 12px 8px;
                margin-top: 15px;

                position: sticky;
                top: 10px;
                z-index: 10;
            }

            .product-image {
                height: 100px;
                width: 100px;
                border-radius: 16px;
                background-color: transparent;
            }

        }

        .product-format-row-container {

            grid-column: 1 / -1;
            border-top: 1px solid $bg2;
            border-bottom: 1px solid $bg2;

            .product-format-line {
                display: grid;
                grid-template-columns: 1fr auto;
                padding: 4px 0;
                color: $text-secondary-color;
            }

        }
        
    }

}

#digital-menu-view.mobile {

    // #rest_topbar .logo-container {
    //   min-height: 70px;
    // }

    #digital-menu-product-list-view .uitable .product-image {
        min-height: 70px;
        min-width: 70px;
    }

    #digital-menu-product-list-view .uitable .cell .label.product-title {
        font-family: $brand-font-2;
        text-transform: lowercase;
        text-transform: capitalize;

        font-size: 1.2em;
    }

    #digital-menu-product-list-view .uitable .cell .product-price {}

    #digital-menu-product-list-view .uitable .menu-section-title {}

}



@media only screen and (max-width: 800px) {

    #digital-menu-view #digital-menu-product-list-view .product-table {
        grid-template-columns: 1fr;
    }

}


.nav-bar-wrapper {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 0;
    margin: 0;
    z-index: 10;

    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

#nav-bar {
    position: relative;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: 8px;
    border: 0;
    padding: 8px;
    margin: 0 auto;
    max-width: 800px;

    .logo-container {
        background-image: url(images/logo.png);
        background-position: center center;
        background-size: contain;
        background-repeat: no-repeat;
        width: 200px;
        
        transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);

        min-height: 100px;

        &.reduce-logo {
            min-height: 40px;
        }
    }

    .back-btn {
        display: block;
        position: relative;
        height: 28px;
        width: 28px;
        background: url(images/turn-back-blue.png) center center no-repeat;
        background-size: contain;
    }

    // .hamburger-btn {
    //   display: flex;
    //   position: relative;
    //   align-items: center;
    //   padding: 8px;
    //   cursor: pointer;

    //   .hamburger-icon {
    //     display: block;
    //     position: relative;
    //     height: 3px;
    //     width: 100%;
    //     max-width: 26px;
    //     background: $action-color;
    //   }

    //   .hamburger-icon::before,
    //   .hamburger-icon::after {
    //     content: '';
    //     display: block;
    //     position: absolute;
    //     height: 100%;
    //     width: 100%;
    //     background: $action-color;

    //     transition: all .2s ease-out;
    //   }

    //   .hamburger-icon::before { top: 7px; }
    //   .hamburger-icon::after { top: -7px; }

    //   &.activated {
    //     .hamburger-icon { opacity: 0; }
    //     .hamburger-icon::before { transform: rotate(45deg); }
    //     .hamburger-icon::after { transform: rotate(-45deg); }
    //   }

    // }

    

}

//styling for hamburger button
.hamburger {
    display: flex;
    align-items: center;
    position: relative;
    height: 26px;
    cursor: pointer;

    .main-nav-toggle {
        display: block;
        position: relative;
        width: 28px;
        height: 16px;

        &:after,
        &:before {
            content: '';
            position: absolute;
            top: 0;
            height: 0;
            border-bottom: 4px solid $action-color;
            width: 100%;
            left: 0;
            right: 0;
            // transition: all ease-out 0.3s;
            transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
        }

        &:after {
            top: 100%;
        }

        i {
            display: block;
            text-indent: 100%;
            overflow: hidden;
            white-space: nowrap;
            height: 4px;
            background-color: $action-color;
            width: 100%;
            position: absolute;
            top: 50%;
            // transition: all ease-out 0.1s;
            transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
        }

        &.active-menu {
            &:after {
                transform: rotate(-45deg);
                transform-origin: center;
                top: 50%;
            }

            &:before {
                transform: rotate(45deg);
                transform-origin: center;
                top: 50%
            }

            i {
                opacity: 0;
            }
        }
    }
}

#nav-window {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    height: 100dvh;
    max-width: 90vw;
    min-width: 35vw;
    padding: 8px;
    z-index: 20;
    
    background: linear-gradient(to right top, rgba(255, 255, 255, 0.82), rgba(255, 255, 255, 0.75));
    color: $action-color;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

    transition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
    transform: translateX(-110%);

    &.show-window {
        transform: translateX(0);
    }


    ul {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        margin: 32px 8px;

        li {
            display: flex;
            flex-direction: row;
            gap: 8px;
            width: 100%;
            align-items: center;

            padding: 8px 32px;

            &:nth-child(odd) {
                background-color: rgba($color: $action-color, $alpha: .1);
            }

            &:hover {
                filter: brightness(1.2);
                background-color: rgba($color: $action-color, $alpha: .2);
            }
        }
    }

    .to-top-btn {
        position: absolute;
        bottom: 16px;
    }
}

#digital-menu-category-list-view.mobile {}


// STYLE CLASSES //////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.bg-panel {
    padding: 16px;

    // background: linear-gradient(to right top, rgba(255,255,255,.42),rgba(255,255,255,.35));
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(3.6px);
    -webkit-backdrop-filter: blur(5.6px);
    border: 1px solid rgba(255, 255, 255, 0.44);

    color: #212529;
}


// ANIMATIONS /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.fade-in {
    animation: fadeIn ease-in-out .4s;

    &.slow {
        animation-duration: 1.2s;
    }

    &.medium {
        animation-duration: .8s;
    }

    &.fast {
        animation-duration: .4s;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}









/////SORT THIS OUT/////////////////////////////////////////////////

// .tmpl-img #choose-menu-view.mobile .main-view-wrapper {
//     margin: 4px !important;
// }


// .desktop .photo-tmpl {
//     box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
//     transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0) !important;
// }

// .desktop .photo-tmpl:hover {
//     will-change: inherit !important;
//     -webkit-transform: none !important;
//     transform: none !important;
//     -webkit-animation-timing-function: inherit !important;
//     animation-timing-function: inherit !important;

//     box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23) !important;
// }



// .ui-navigation-controller>div {
//     position: relative !important;
//     background: none !important;
// }




#digital-menu-preset-selection-view {

    display: flex;
    flex-direction: column;

    // background: url("img/bg1.jpg") no-repeat;
    // background-size: cover;
    min-height: 100vh;
    padding: 0;
    margin: 0;
    
    p { line-height: 1.2em; }
    h1 { line-height: 1.4em; }

    .sub-text {
        font-size: 70%;
        color: #9f9eac;
    }

    #nav-window .icon {
        width: 70px;
        height: 70px;
    }

    .bg-video-container {
        position: absolute;
        display: block;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: -1;

        #bg-video {
            position: fixed;
            right: 0;
            top: -200px;
            min-width: 100%;
            min-height: 100%;
        }

        .screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            background: linear-gradient(0deg, rgba(0,0,0,0) 10%, rgba(0, 0, 0, .3) 90%);;
        }

        .content {
            position: fixed;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            color: #f1f1f1;
            width: 100%;
            padding: 20px;
        }

        /* Style the button used to pause/play the video */
        #myBtn {
            width: 200px;
            font-size: 18px;
            padding: 10px;
            border: none;
            background: #000;
            color: #fff;
            cursor: pointer;
        }

        #myBtn:hover {
            background: #ddd;
            color:  black;
        }
    }

    .content-container {
        display: flex;
        flex-direction: column;
        justify-items: center;
        gap: 16px;

        margin: 0 auto;
        margin-top: 150px;
        margin-bottom: 100px;
        padding: 0 8px;
        width: 100%;
        max-width: 800px;
    }

    
    .hero-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        

        h1 {
            font-size: 300%;
            font-weight: 700;
            color: #ffffff;
        }
        p { 
            font-size: 150%;
            color: #ffffff;
        }
    }

    .section-info-container {
        position: relative;
        width: 100%;
        margin: 0 auto;
        height: auto;

        h1 {
            font-size: 150%;
            font-weight: 700;
        }
    }

    .venue-info-container {
        display: grid;
        grid-template-columns: 1fr 3fr;
        grid-template-rows: auto 1fr;
        gap: 16px;

        h1 {
            font-size: 150%;
            font-weight: 700;
        }
    }

    .icon {
        transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
        cursor: pointer;
        color: $action-color;

        display: block;
        width: 150px;
        height: 150px;
        border-radius: 4px;

        background-color: white;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;

        &.lento { background-image: url(images/lento.jpg); }
        &.aptitude { background-image: url(images/aptitude.jpg); }
        &.derwandi { background-image: url(images/derwandi.jpg); }
        &.estkanahchai { background-image: url(images/estkanahchai.jpg); }
        &.lgymat { background-image: url(images/lgymat.jpg); }
        &.sugarcoat { background-image: url(images/sugarcoat.jpg); }
        &.wintertreats { background-image: url(images/wintertreats.jpg); }
        &.glaze { background-image: url(images/glaze.jpg); }
        &.treats { background-image: url(images/treats.jpg); }
        &.dual-link { background-image: url(images/dual-link.jpg); }
        &.pinocchio { background-image: url(images/pinocchio.jpg); }
        &.solysombra { background-image: url(images/solysombra.jpg); }
        &.laola { background-image: url(images/laola.jpg); }

        &:hover {
            filter: brightness(1.2);
            transform: scale(1.1);
        }
    }

    // .preset-grid {
    //     display: grid;
    //     position: relative;
    //     grid-template-columns: 1fr;
    //     gap: 24px;
    //     width: 100%;
    //     margin: 0 auto;
    //     height: auto;
        
    //     a {
    //         display: block;
    //         width: 150px;
    //         height: 150px;
    //     }
    // }




    footer {
        background-color: #ffffff;
        padding: 32px 8px;

        .footer-content-container {
            display: flex;
            gap: 16px;
            margin: 0 auto;
            padding: 0 8px;
            width: 100%;
            max-width: 800px;

            .sponsor-logo {
                max-height: 60px;
            }
        }
    }

    .sub-footer {
        background-color: #F0F0F0;

        .sub-footer-container {
            display: flex;
            gap: 16px;
            margin: 0 auto;
            padding: 8px;
            width: 100%;
            max-width: 800px;
            padding: 8px 0;

            .copy-right {
                font-size: 70%;
                color: #888888;
            }
        }
    }

    
}

@media (max-width: 800px) {
    /* … */
    #digital-menu-preset-selection-view .preset-grid {
        
        grid-template-columns: 1fr;
    }
  }

  @media (max-width: 600px) {
    /* … */
    #digital-menu-preset-selection-view .preset-grid {
        
        grid-template-columns: 1fr;
    }
  }

  @media (max-width: 360px) {
    /* … */
    #digital-menu-preset-selection-view .preset-grid {
        
        grid-template-columns: 1fr;
    }
  }