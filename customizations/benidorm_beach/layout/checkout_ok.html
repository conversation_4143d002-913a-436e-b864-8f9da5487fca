<!DOCTYPE html>
<html>
<head>
    <title>Thank you for your order</title>
    <meta charset="utf-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1.0, width=device-width, maximum-scale=1">

    <link href="animate.min.css" rel="stylesheet" />
    <link href="{{CSS_URL}}" rel="stylesheet" />
</head>
<body>

  <div id="checkout-ok-view" class="text-center">

    <div class="oocol oopx-2 oomx-auto" style="max-width: 1200px ;">
      <div class="spin-loader"></div>
    </div>

    <div class="col px-2 mx-auto" style="max-width: 1200px;">

      <div class="cell rounded oomy-9 oomx-9">
        <div class="hero oopy-9 oopx-4 title-regular glass-panel">
          <div class="title text-bold" data-localized-key="ORDER NUMBER">NUMERO DE PEDIDO</div>
          <div class="order-num text-bold oomb-8">{{ORDER_ID}}</div>
          <div class="thank-you" data-localized-key="THANK YOU MSG">¡Gracias por tu compra!</div>
          <div class="sent-email"><span data-localized-key="EMAIL MSG">Hemos enviado un email con tu pedido a</span> {{CLIENT_EMAIL}}</div>
          <div class="closing" data-localized-key="CLOSING MSG">Si tienes alguna duda o quieres cambiar tu pedido, llámanos al {{PHONE}} o escr&iacute;benos al {{EMAIL}}</div>
        </div>
      </div>

      <div class="oorow">
        <div class="flex-1"></div>
          <a href="{{MAIN_URL}}" style="text-decoration: none;">
            <div class="title-btn oobtn oobtn-primary oobtn-cta" data-localized-key="MAKE ANOTHER ORDER">MAKE ANOTHER ORDER</div>
          </a>
          <div class="flex-1"></div>
      </div>

    </div>

  </div>

  <!-- <script>
    if ( "{{DISPLAY_ORDER}}" !== "block" )
         setTimeout( function ( )
         {
            location.reload( ) ;
         }, 2000 ) ;
  </script> -->

</body>
</html>