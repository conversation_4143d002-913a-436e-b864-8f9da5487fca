
// DECLARE NEW FONTS //////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

  @font-face {
    font-family: "Daft Brush";
    src:  url('fonts/DaftBrush-Text.woff2') format('woff2');
    font-style: normal;
    font-weight: normal;
  }

  @font-face {
    font-family: "Neutra Text";
    src:  url('fonts/NeutraText-Book.woff2') format('woff2');
    font-style: normal;
    font-weight: normal;
  }

  @font-face {
    font-family: "Neutra Text";
    src:  url('fonts/NeutraText-Bold.woff2') format('woff2');
    font-style: normal;
    font-weight: 700;
  }



// VARIABLES //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

//color in webpage
$color1: #a5ae97;
$color2: #5B4E41;
$color3: #f1e6d0;
$color4:#9A7F62;
$btn-hover: #43545C;

$font-family: "Neutra Text", Sans-serif !important;
$font-family2: "Daft Brush", Sans-serif;


$action-color: #a5ae97;
$action-color-dark: #939d83;

$bg1: rgba(255,255,255,1); 
$bg2: #ffffff;
$fg1: #9A7F62;
$stripe-color: rgba(0,0,0,.05);
$curtain_color: $bg1;

$border-color: #f1e6d0;
$text-primary-color: #5B4E41;
$text-secondary-color: #AFAFBD ;
$text-inverted-color: #FFFFFF ;

$price-color: #a5ae97;



// BODY PROPERTIES ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

* {
  box-sizing: border-box;

    //this fixes the text rendering problems with Mac devices
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

html {
  //background-color:   // used for simulating bg of webpage iframe for testing
  box-sizing: border-box !important;

  background-color: $bg1;
  background: none;
  background-size: cover;
  min-height: 100%;

  font-family: $font-family;
  font-size: 18px !important;
  font-weight: normal;
  font-style: normal;
  color: $text-primary-color;
}

body {
  position: relative !important;
  display: block !important;
  height: auto !important;
  width: 100% !important;
  color: inherit !important;
  background: none !important;

  font-family: $font-family;
  font-size: 18px !important;
  font-weight: normal;
  font-style: normal;
  color: $text-primary-color;

    //this fixes the text rendering problems with Mac devices
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

a {
  color: $action-color !important;
  transition: all ease-in-out .15s;

  &:hover {
    filter: brightness(1.2);
  }
}

.bg-bg1 {
  background-color: transparent !important;
}

/// SCROLL BAR PROPERTIES ////////////// 

/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #aaa;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: #aaa;
}

*::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  border: 3px solid #aaa;
}

.selected .checkbox-button::before, 
.checkbox-button.selected::before, 
.custom-selected 
.checkbox-button::before {
  border-bottom: 4px solid $action-color;
  border-right: 4px solid $action-color;

}

.flex-wrap {
  flex-wrap: wrap;
}

.sticky-top {
  top: -1px;
}

// GENERAL MODUALS ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.oobtn {
  transition: all ease-in-out .1s;

  &.oobtn-primary {
    background-color: $color2;
    border-radius: 25px;
    color: $text-inverted-color;

    // background-image: linear-gradient(to right, $color2, $color2);

    &:hover {
      background-color: $btn-hover;
    }
  }
  &.oobtn-secondary {
    border: 1px solid $color2;
    border-radius: 2px;
    color: $color2;
    font-weight: bold;

    &:hover {
      border-color: $btn-hover;
      color: $btn-hover;
    }
  }
}

.btn-minus, .btn-plus, .oobtn-icon {
  transition: all ease-in-out .15s;
  cursor: pointer;
  color: $action-color;

  &:hover {
    filter: brightness(1.2);
  }
}

input, textarea, .combobox, select {
  
  box-sizing: border-box !important;
  font-family: $font-family !important;
  // background: rgba(0, 0, 0, .1) !important;

  // FIX: campos no se alinean bien en producción, falta width de los input
  width: 100%;

  & option {
      // background: rgba(0, 0, 0, .9);
      // color: #fff;
      // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
  }
}


// prefered no background as default
#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
    background: transparent;
    overflow-y: auto;
    height: 100dvh;
    width: 100%;
    box-sizing: border-box !important;
}

.modal_window {
  color: $text-primary-color;
  border: 1px solid rgba(255, 255, 255, 0.33) !important;
  background-color: $bg1;
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22) !important;
  border-radius: 2px;
}

.footer-m {
  background: linear-gradient(to right bottom, rgba(255,255,255,.4),rgba(255,255,255,.3)) !important;
}

.spin-loader {
  border: 3px solid $action-color !important
}

.stepper .dot {
  width: 7px;
  height: 7px;
}

.stepper .dot.selected {
  background-color: $action-color;
}

.stepper .dot.selected::before {
  border: 1px solid $text-primary-color;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.selected .radio-button::before, .custom-selected .radio-button::before {
  background-color: $action-color;
}

.button-close  {
  &.cta {
    color: $action-color !important;
    background: transparent !important;
  }
}

input:focus,
textarea:focus {
  border-color: $action-color !important;
  outline-color: $action-color !important;
  outline: 0 !important;
}


// TABLE PROPERTIES ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#product-table-scroller {
  scroll-behavior: smooth;
}

.cell {
  border: none !important;
}

.hero {
  background: none;
  color: $text-primary-color;
}

.photo-tmpl .product-image {
  margin: 0;
  border-radius: 0px !important;
  border-top-right-radius: 11px !important;
  border-top-left-radius: 11px !important;
  background-color: #ffffff;
  background-repeat: no-repeat;
  height: auto;
  min-height: 235px;
  background-size: 106% !important;
  border: 0px;
  border-bottom: 1px solid lightgray;
} 


.mobile .product-table,
.desktop .product-table { 

  display: grid !important;
  grid-template: auto / auto;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  padding-left: 8px;
  padding-right: 8px;

  .section-header {
    background-color: $bg1;
    border-bottom: 2px solid rgba(0, 0, 0, .2);
  }

  .menu-section-header,
  .menu-section-title {
    grid-column: 1 / -1 !important;
    box-sizing: border-box;

    font-family: $font-family2 !important;
    font-weight: 400;
    font-size: 150%;
    letter-spacing: 1px;
    color: $color2 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.33);
      // background: none;
      // font-family: 'Great Vibes', cursive !important;
      // font-size: 220%;
      // text-align: center;
      // // border-top: 3px double #363636;
      // // border-bottom: 3px double #363636;
      // margin-top: 12px;
      // margin-bottom: 12px;
  }



  .cell {
    width: 100% !important;
    margin: 0 !important;
    background-color: rgba(255,255,255,.5);
    color: $text-primary-color;


    &>div {
        // border-radius: 1px solid $text-primary-color;
        // outline: 5px solid $bg2;
    }

    .hero {
      margin-top: 0 !important;
      background-color: transparent !important;
      color: $text-primary-color;
      border: 0;
    }
  
      // .image-cover {
      //     background-size: 105% auto !important;
      // }
      
      
  }

  
}

// make cells not rounded
.cell.rounded {
  // border-radius: 0 !important;
  // overflow: hidden;
}

.desktop #product-list-view > .product-table > .cell.product {
  // width: 233px !important;
  // min-width: 220px;
  // // flex: 1;
  // border-radius: 0px !important;
}




// PICKUP OR DELIVERY /////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#pickup-or-delivery 
{
  .pickup-icon, 
  .take-away-icon,
  .table-icon {
    width: 150px;
    height: 150px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    border: 1px solid $action-color;
    border-radius: 50%;
    padding: 16px;
    background-size: 70% !important;
    background-color: $bg1;
  }

  .take-away-icon {
    background-image: url(images/take_away_green.png) !important;
  }
  .pickup-icon {
    background-image: url(images/pickup_green.png) !important;
  }
  .table-icon {
    background-image: url(images/table_green.png) !important;
  }

  .take-away-block {}
  .pickup-block {}
  .table-block {}

  .hl-title-bold {
    font-family: $font-family !important;
    color: $text-primary-color !important;
    letter-spacing: 1px;
    font-size: 130%;
    font-weight: 400;
  }

  .logo-container {
    background: url("images/logo.svg") center center;
    background-repeat: no-repeat;
    background-size: contain;

    min-height: 150px;
    width: 100%;
    max-width: 350px; 
  }
}



// CHOOSE MENU VIEW ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.desktop .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr) 360px;
  padding-left: 8px;
  padding-right: 8px;

  .ticket-view-wrapper {
    grid-row: 1/3;
    grid-column: 2/3;
  }
}

.mobile .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr);
  padding: 8px 8px 0 8px;
}


/////////////////// CATEGORY MENU VIEW //////////////////////////

#category-menu-view {
  position: relative !important;;

  font-size: 13px;
  letter-spacing: .05em;
  text-transform: uppercase;
  font-weight: 400;
  border-radius: 0;

  .glass-panel {
    border-radius: 2px !important;
  }

  .scroll-row {
    position: relative !important;
  }

  .menu-item {

    position: relative !important;

      &:hover,
      &.selected {
        background-color: $action-color;

        .oobtn-link {
          color: $text-inverted-color !important;
        }
        
      }
  }

  .oobtn-link {
      color: $text-primary-color;
      font-weight: bold;
  }

  .oobtn-next, .oobtn-prev {
    position: relative !important;
    
    display: flex;
    align-items: center;
    
  }

    

  .oobtn-next:hover:before, .oobtn-prev:hover:before, .oobtn-link:hover {
      opacity: 1 !important;
      filter: brightness(1.2);
  }


  .btn-minus.disabled, .btn-plus.disabled, .oobtn-icon.disabled {
      color: #aaaaaa;
  }
}



/////////////////// PRODUCT LIST VIEW //////////////////////////

#product-add-view {
  height: auto !important;
}

.photo-tmpl.selected {
  border: 0 !important;

  &.glass-panel::after {
    background: linear-gradient(to right bottom, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.40)) !important; 
  }
}

.cell.rounded {
  border-radius: 2px;
}

.align-right-row {
  text-align: right;
  justify-content: flex-end;
}

.product-price {
  background-color: $action-color;
  color: $text-inverted-color;
  padding: 4px 8px;
  flex: inherit;
  margin-left: auto;
}

/////////////////// TICKET VIEW //////////////////////////

.desktop #ticket-view {
  width: 100%;
  position: relative !important;
  top: inherit !important;
}

#checkout-date-picker-view {
  height: auto !important;
}

.custom-stripe > .stripped:nth-child(2n),
.stripe > div:nth-child(2n) {
  background-color: $stripe-color;
}

.ticket-view-title {
  font-size: 150%;
  font-weight: 400;
  font-family: $font-family2 !important;
  color: $color2 !important;
  letter-spacing: 1px;
}


// CHECK OUT VIEW /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

#checkout-view.desktop .main-view-wrapper {
  padding-right: 8px;
}


#client-info-view,
#conditions-container,
#terms-wrapper {
    background-color: transparent !important;
    border-radius: 0px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    margin-top: 15px;
    color: $text-primary-color;

    .cell .hero {
        background-color: transparent !important;
        border-radius: 0 !important;
    }
}

.tab.selected {
  font-family: $font-family2 !important;
  border-color: $text-primary-color;
  letter-spacing: 1px;
  color: $color2;
  font-size: 150%;
  font-weight: 400;
}

#terms-wrapper {
  margin-left: 10px !important;
  margin-right: 10px !important;
  background-color: rgba(0, 0, 0, 0.7);
  border: 0;
  margin-top: 16px;
}

#payment-modal-view {
  
}

#client-info-view {
  .oobtn-secondary {
    padding: 2px 4px;
  }
}


// STYLE CLASSES //////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.glass-panel {

  /* From https://css.glass */
  // background: rgba(255, 255, 255, 0.15) !important;
  position: relative;
  background: none !important;
  // background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
  // border-radius: .8em !important;
  // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
  // backdrop-filter: blur(5.3px) !important;
  // -webkit-backdrop-filter: blur(5.3px) !important;
  // border: 1px solid rgba(255, 255, 255, 0.33) !important;

  color: $text-primary-color !important;

  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;

    background-color: $bg2;
    // background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
    border-radius: 3px !important;
    // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(5.3px) !important;
    -webkit-backdrop-filter: blur(5.3px) !important;
    border: 1px solid $border-color !important;
  }
}

.bg-panel {

  position: relative;
  background: none !important;

  color: $text-primary-color !important;

  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;

    border-radius: 11px !important;
    border: 1px solid $border-color !important;
    background-color: $bg2;

    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    // background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
    // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
  }
}

// ANIMATIONS /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.fade-in {
  animation: fadeIn ease-in-out .4s;

  &.slow { animation-duration: 1.2s; }
  &.medium { animation-duration: .8s; }
  &.fast { animation-duration: .4s; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}









.tmpl-img #choose-menu-view.mobile .main-view-wrapper {
    margin: 4px !important;
}


.desktop .photo-tmpl {
    // box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    transition: all 0.3s cubic-bezier(.25,.8,.25,1) !important;
}

.desktop .photo-tmpl:hover {
    will-change: inherit !important;
    -webkit-transform: none !important;
    transform: none !important;
    -webkit-animation-timing-function: inherit !important;
    animation-timing-function: inherit !important;
    border-radius: 11px;
    // box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23) !important;
}



.ui-navigation-controller > div {
  position: relative !important;
  background: none !important;
}












