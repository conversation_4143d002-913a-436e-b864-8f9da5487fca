////////////////////////////////////////////
// Theme for 528
// 
// NOTES
// Iframe background color: <PERSON><PERSON><PERSON> sits in a popup window with background-color: rgba( 53, 44, 93, 0.74 )
// font-family: "gil-sans-light", sans-serif  font-weight: 700
// NO GOOGLEAPIS LINK FOUND
////////////////////////////////////////////


@font-face {
    font-family: 'gil-sans-light';
    src:  url('fonts/5402Gil-Sans-light.woff2') format('woff2');
}

$font-family: 'gil-sans-light', sans-serif;

$action-color: #60C4B7;
$bg1: transparent;
$fg1: #666666 ;
$bg2: rgba(255, 255, 255, .1);
$stripe-color: $bg2;

$border-color: #cccccc;
$text-primary-color: #FFFFFF;
$text-inverted-color: #FFFFFF;

//$text-secondary-color: #d5d5de;
$text-secondary-color: #FFFFFF;

$badge-text-color: $text-inverted-color ;

$eye-catching-color: #60c4b7 ;

$input-border-color: #e5e5e5 ;
$separator-color-bg1: #c8c8c8 ;
$separator-color-bg2: #e0e0e0 ;
$radio-button-border-color: #707070 ;

$disabled-color: $separator-color-bg1 ;

$input-bg-color: rgba(255,255,255,.2);
$hover-color: lighten( $action-color, 40% ) ;

//
// THEME SPECIFIC COLORS
// BIG PHOTO THEME
$theme_image_bg1: $bg1 ; // used in product square below the image
$theme_image_fg1: $text-primary-color ; // used in the text color of the product and modifiers
$theme_image_bg2: #f5f5f5 ; // used in modifiers

// Classes to customize things in rare ways:
// .menu-section-title
// .product-price, .modifier-price
// .product-title

$shadow-color: #0006 ;
$danger-color: #ff5544;
$curtain-color: #0009 ;

$price-color: #77a464;

iframe {
    border: 0;
}

html {
    //background-color: rgba( 53, 44, 93, 0.74 );  // used for simulating bg of webpage iframe
    scrollbar-color: #888 #aaa;
    scrollbar-width: thin;
}

body,
.bg-bg1 { background: none !important; background-color: inherit !important; }

.product-price,
.modifier-price { color: $price-color !important; }

input, textarea, .combobox, select {
    font-family: 'gil-sans-light', sans-serif !important;
    background: rgba(0, 0, 0, .1) !important;
    color: #ffffff !important;

    width: 100%;

    & option {
        background: rgba(0, 0, 0, .9);
        // color: #fff;
        // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
    }
}

// Remove incremental arrows from Input (Number) /* Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

// Remove incremental arrows from Input (Number) /* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

table.calendar-days-view > tbody > tr > td.day-cell.day-6:not(.selected) > div,
table.calendar-days-view > tbody > tr > td.day-cell.day-0:not(.selected) > div {
    background-color: rgba(250,250,250,.1) !important;
}

table.calendar-days-view > tbody > tr > td.selected > div,
.time-range-cell.selected {
    background-color: rgba(96, 196, 183, .8) !important;
}

table.calendar-days-view > tbody > tr > td.disabled > div {
    color: #7e7e7e !important;
}

.calendar-form-view .calendar-nav .calendar-prev-btn,
.calendar-form-view .calendar-nav .calendar-next-btn {
    display: flex;
    justify-content: center;
    align-items: center;
}

.oobtn-primary {
    height: 40px !important;
    border-radius: 50px !important;
    color: #ffffff !important;
    font-weight: bold !important;
    padding: 0px 35px !important;
    background-color: #49013a !important;
    box-sizing: border-box !important;
    -webkit-box-sizing: border-box !important;
    border: 1px solid rgba(0,0,0,.05) !important;
    letter-spacing: .1em !important;
    font-family: "gil-sans-light", sans-serif !important;
    line-height: normal !important;

    -webkit-box-shadow: inset 1px 1px 0 0 rgba(255,255,255,.1),inset 0 1.8em 30px 0 rgba(255,255,255,.2);
    box-shadow: inset 1px 1px 0 0 rgba(255,255,255,.1),inset 0 1.8em 30px 0 rgba(255,255,255,.2);

    transition: background-color .3s, color .3s;
    -webkit-transition: background-color .3s, color .3s;
    -o-transition: transform .3s,background-color .3s, color .3s;

    &:hover {
        color: #93c0bd !important;
        background-color: #3b012f !important;
    }

    &:disabled,
    &.disabled {
        cursor: auto !important;
        background-color: #494949 !important;
        color: #93c0bd !important;
        opacity: .5 !important; // REASON: framework puts opacity = 0.1
    }
}

#booking-ok {
    margin: 0 auto !important;
}

#online-payment-view {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    justify-items: center;
    align-content: center;
    justify-content: center;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url('https://528ibiza.com/wp-content/uploads/2022/06/KN1-XvNg-scaled.jpeg') center center !important;
    background-size: cover !important;
    padding: 8px;
    box-sizing: border-box;

    >.cell{
        border-radius: 0px;
        border: 1px solid #ffffff;
        box-shadow: 0px 10px 25px 21px rgba( 2, 2, 2, 0.50 );
        background-color: rgba( 53, 44, 93, 0.92 );
        min-width: auto !important;

        // hide payment details table - only for 528
        >.oop-4{
            display: none !important;
        }
    }

    #logo {
        width: 100% !important;
        height: 100px !important;
        background-position: center center !important;
    }

    .payment-method-table {
        background: none !important;
    }

    .radio-button {
        border-color: #ffffff !important;
      }
  
}