.stripe
{
  background-color: #f3f3f3 ;
}

.btn-switch {
    height: 40px ;
    padding: 3px ;
    border-radius: 4px ;
}

.btn-switch > .btn {
    height: 32px ;
    text-transform: uppercase ;
    padding: 0 1em ;
    border-radius: 3px ;
    font-size: 12px ;
    font-weight: 600 ;
}

.btn-switch > .btn.btn-danger {
    background: linear-gradient(to bottom, #DD1317, #A71215);
    cursor: default ;
}

.btn-switch > .btn.btn-outline-danger:hover {
    background: transparent ;
    text-decoration: underline ;
    color: #DD1317 ;
}


.container {
    max-width: 1280px ;
}


html, body {
  min-height: 100%;
  font-family: 'Montserrat';
}


.rbn { position: absolute ; }
.rbn-offer
{
    overflow: hidden ;
    width: 80px;
    height: 80px;
    top: 0 ;
    left: 0 ;
}

.rbn-offer > div {
  background: linear-gradient(to top, #84090C, #880A0D 8%, #C91216);
  padding: 3px 6px ;
  color: #fff ;
  font-size: 9px ;
  transform: rotate(-45deg);
  position: absolute ;
  top: 25px;
  right: -30px;
  width: 155px;
  text-align: center;
}    

.rbn-promotion
{
    background-color: #8DBD32 ;
    padding: 3px 6px ;
    border-radius: 6px ;
    color: #101010 ;
    top : 9px ;
    right: 5px ;
    font-size: 8px ;
    font-weight: 600 ;
}


.fs-10 {
    font-size: 10px ;
}

.fs-12 {
    font-size: 12px ;
    font-weight: 500 ;
}


.fs-14 {
    font-size: 14px ;
}

.fs-16 {
    font-size: 16px ;
}

.fs-20 {
    font-size: 20px ;
}

.fs-30 {
    font-size: 30px ;
}


.delivery-type-control {
    width: 190px ;
}

.delivery-type-control .switch-label {
    margin-bottom: 6px;
}



.address-control {
    width: 475px ;
}

.address-label { margin-bottom: 6px ; }
#search-box input, .address-control input {
    height: 40px ;
    padding-left: 1em ;
    padding-right: 1em ;
}




#search-box {
  height: 182px;
  background: url(../images/hero-image.jpg) no-repeat center;
  background-size: cover;
  background-position: 156px 50% ;
  background-color: #f3f3f3 ;
  padding: 45px 0px 45px 120px ;
}

#search-box input {
    height: 40px ;
    width: 475px ;
}

#search-box select {
    height: 40px ;
    width: 120px ;
}



#food > .container, #restaurants > .container
{ padding: 0 55px ; }

.cr-left, .cr-right { width: 30px ; font-size: 26px; cursor: pointer; }
.cr-item, .cr-img { width: 250px ; }
.cr-item { cursor: pointer ; }

.cr-img
{
    height: 188px ;
    background-size: cover ;
}

.cr-lbl.cr-lbl-over
{
    height: 20px ;
    margin-top: -20px ;
    padding: 4px 8px ;
    background-color: rgba(0, 0, 0, 0.4 ) ;
    color: white;
    font-size: 12px ;
    line-height: 12px ;
}


#restaurants .card { cursor: pointer }
#restaurants .card-img-top
{
  background-size: cover ;
  background-position: center ;
  height: 208px ;
}
#restaurants .card a { text-decoration: none ; color: black ; }
#restaurants .card a:hover { color: red ; }


#footer-1 a { text-decoration: none ; color: white ; }
#footer-1 a:hover { color: red ; }
.bg-black { background-color: #000 ; color: white ; }

#footer-2 a { text-decoration: none ; color: black ; }
#footer-2 a:hover { color: red ; }

/*
:root {
  --background: #fff;
  --text-color-white: #fff;
  --text-color-primary: #101010;
  --text-color-secondary: rgba(16, 16, 16, 0.7);
  --background-gradient-red: linear-gradient(to right, #84090C, #C91216);
  --background-grey: #f4f4f4;
  --stopper: rgba(136, 10, 13, 0.1);
  --primary-color: #DD1317;
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a,
a:hover,
a:active,
a:focus {
  text-decoration: none;
  color: var(--text-color-primary);
}

html,
body {
  min-height: 100%;
  font-family: 'Montserrat-Regular';
}

li {
  list-style-type: none;
}

nav {
  min-height: 120px;
}

main {
  height: 100%;
}

footer {
  height: auto;
  
}

.logo-ara img {
  height: 84px;
}

input[type="text"] {
  padding: 5px 10px;
  width: 90%;
  outline: none;
  border: 1px solid var(--text-color-secondary);
  border-radius: 3px;
}

input[type="text"]:focus {
  border-color: var(--primary-color);
}

#search-box {
  height: 182px;
  background: url(../images/hero-image.jpg) no-repeat center;
  background-size: cover;
  background-position: 156px 50% ;
  background-color: #f3f3f3 ;
}

.search > label,
.delivery > p {
  color: var(--text-color-secondary);
  font-size: 12px;
}

select {
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  color: var(--text-color-primary);
  outline: none;
}

#food {
  height: auto;
}

#food .content .food-item img,
#top-dishes .content .food-item img {
  max-width: 200px;
  max-height: 150px;
}

#restaurants {
  background-color: var(--background-grey);
}

.button-group {
  border: 1px solid var(--primary-color);
  border-radius: 3px;
  padding: 8px;
}

.button {
  width: 95px;
  height: 32px;
  color: var(--primary-color);
  border-radius: 3px;
  cursor: pointer;
}

.button-selected {
  background: var(--primary-color);
  color: var(--text-color-white);
}

.search-input-food {
  width: 400px !important;
}

.search-input-food::placeholder {
  font-size: 12px;
}

.arrow-right,
.arrow-left {
  width: 15px;
}

.arrow-right img,
.arrow-left img {
  width: 15px;
  cursor: pointer;
}

#restaurants .restaurant-item {
  width: 300px;
  background-color: var(--background);
  box-shadow: 1px 1px 5px var(--text-color-secondary);
}

#restaurants .restaurant-item img {
  max-width: 300px;
  max-height: 170px;
}

#restaurants .restaurant-item .description {
  font-size: 12px;
  color: var(--text-color-secondary);
}

#why .why-descriptions {
  color: var(--text-color-primary);
  line-height: 1.5;
}

#why .phone-image {
  max-height: 300px;
}
#how-to-order {
  background-color: var(--background-grey);
}

#how-to-order img {
  max-width: 100%;
}

footer .logo-aravoy-footer {
  height: 80px;
}

footer #footer-1 {
  color: var(--text-color-white);
  background: #000
}

#footer-1 a {
  color: white;
}

#footer-2 .box-payments img {
  width: 64px;
}

#footer-2 .logo-ara-footer {
  width: 200px;
}

.wrapper {
  width: 100%;
  height: 100%;
}

.d-flex {
  display: flex;
}

.d-flex-column {
  display: flex;
  flex-direction: column;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between{
  justify-content: space-between;
}

.justify-content-evenly {
  justify-content: space-evenly;

}

.justify-content-around{
  justify-content: space-around;
}

.align-items-center {
  align-items: center;
}

.align-items-stretch {
  align-items: stretch;
}

.align-self-center {
  align-self: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.m-10 {
  margin: 10px;
}
.m-15 {
  margin: 15px;
}
.m-20 {
  margin: 20px;
}
.m-25 {
  margin: 25px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.p-10 {
  padding: 10px;
}

.p-15 {
  padding: 15px;
}

.p-20 {
  padding: 20px;
}

.p-25 {
  padding: 25px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-50 {
  padding-left: 50px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
v}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-50 {
  padding-right: 50px;
}

.ptb-25 {
  padding-top: 25px;
  padding-bottom: 25px;
}

.ptb-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}

.plr-25 {
  padding-left: 25px;
  padding-right: 25px;
}

.plr-30 {
  padding-left: 30px;
  padding-right: 30px;
}

.plr-50 {
  padding-left: 50px;
  padding-right: 50px;
}

.bold {
  font-family: 'Montserrat-Bold';
}

.color-red {
  color: var(--primary-color);
}

.text-underline {
  text-decoration: underline;
}

.text-align-center {
  text-align: center;
}

.font-size-14 {
  font-size: 14px;
}

*/

