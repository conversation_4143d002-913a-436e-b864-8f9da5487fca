////////////////////////////////////////////
// Theme for ____________________
// 
// NOTES:
// Iframe background color: 
// font-family: 
////////////////////////////////////////////

// add font files here and googleapi fonts in header.html
// @font-face {
//     font-family: 'gil-sans-light';
//     src:  url('fonts/5402Gil-Sans-light.woff2') format('woff2');
// }

$font-family: 'Open Sans', sans-serif ;

$action-color: #00bebe;
$action-color-dark: #1b78a7;
$bg1: #FFFFFF ;
$fg1: #000000 ;
$bg2: #f1f4f5 ;
$stripe-color: #f5f5f5 ;

$border-color: #ebebeb ;
$text-primary-color: #000000 ;
$text-secondary-color: #989898 ;
$text-inverted-color: #FFFFFF ;

$input-bordercolor: #e5e5e5 ;
$input-bg-color: rgba(255,255,255,.2);
$hover-color: lighten( $action-color, 40% ) ;

$shadow-color: #0006 ;
$danger-color: #ff5544;
$curtain-color: #0009 ;

$price-color: #77a464;

iframe {}

html {
    //background-color:   // used for simulating bg of webpage iframe for testing
    scrollbar-color: #888 #aaa;
    scrollbar-width: thin;
}

// prefered no background as default
body,
.bg-bg1 {  }

// .product-price,
// .modifier-price { color: $price-color !important; }

// styles for callendar and other inputs
input, textarea, .combobox, select {
    // font-family: 'gil-sans-light', sans-serif !important;
    // background: rgba(0, 0, 0, .1) !important;
    // color: #cccccc !important;

    // FIX: campos no se alinean bien en producción, falta width de los input
    width: 100%;

    & option {
        // background: rgba(0, 0, 0, .9);
        // color: #fff;
        // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
    }
}

// Remove incremental arrows from Input (Number) /* Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

// Remove incremental arrows from Input (Number) /* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

button {  }

.oobtn-primary {
    // button styles

    &:hover {
        // hover on button styles
    }

    &:disabled,
    &.disabled {
        // disabled button styles
    }

}



// DIGITAL MENU ///////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.view, .window {
    position: relative;
    display: block;
  }
  
  .gap {
    gap: 8px;
  }
  
  
  #digital-menu-view {
  
    .grid-row {
      display: grid;
      gap: 8px;
      grid-template-columns: 1fr auto;
    }
  
    .main-view-wrapper {
      display: flex;
      gap: 8px;
      flex-direction: column;
    }
  
  }
  
  
  #digital-menu-list-view {
    position: relative !important;
    display: flex;
    flex-direction: column;
    flex: 1;
  
    top: 80px !important;
    padding: 0 !important;
  
    .spin-loader-container {
      position: absolute;
      display: block;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
    }
  
    .uitable {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      width: 100%;
      overflow-y: auto !important;
      overflow-x: auto !important;
      //give some space after last cell
      padding-bottom: 32px;
      padding-top: 8px;
  
      .cell {
        position: relative;
        display: grid;
        grid-template-columns: auto 1fr;
        gap: 16px;
        height: auto;
        padding: 8px;
  
        .grid-row {
          padding: 8px;
        }
  
        .label.product-title {
          font-size: 16px;
          font-family: 'Proxima Nova', sans-serif;
          font-weight: bold;
        }
      }
  
      .menu-section-title {
        padding: 16px 8px;
        font-size: 160%;
        margin-top: 50px;
      }
  
      .product-image {
        min-height: 100px;
        min-width: 100px;
        border-radius: 16px;
        background-color: transparent;
      }
  
    }
  }


  
@media only screen and (max-width: 800px) {
  
    #digital-menu-list-view .product-table {
      grid-template-columns: 1fr;
    }
  
  }