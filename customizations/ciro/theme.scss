
// DECLARE NEW FONTS //////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.woff') format('woff');
  font-weight: 100;
  font-style: normal;
}


@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.woff') format('woff');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
}


@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.woff') format('woff');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
}



// VARIABLES //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

$font-family: "SF","Roboto", sans-serif ;
// #00BEBE (light)   (dark)
$action-color: #00bebe;
$action-color-dark: #1b78a7;
$bg1: rgba(0,0,0,.8) ; // #F9F4EE ;
$bg2: rgba(255,255,255,.3);
$fg1: #4b3918 ;
$stripe-color: rgba(255,255,255,.2);
$curtain_color: $bg1;

$border-color: #D0D0DB ;
$text-primary-color: #FFFFFF; //#1A1A1A ;
$text-secondary-color: #AFAFBD ;
$text-inverted-color: #FFFFFF ;

$price-color: #77a464 ;

$border-radius: 8px;

// BODY PROPERTIES ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

* {
  box-sizing: border-box;
}

html {
  //background-color:   // used for simulating bg of webpage iframe for testing
  font-size: 18px !important;
  font-weight: 400 !important;
  box-sizing: border-box !important;

  background-color: $bg1;
  background: url("images/bg1.jpg") no-repeat;
  background-size: cover;
  min-height: 100%;

  font-family: $font-family;
  font-weight: normal;
  font-style: normal;
  color: #212529;
}

body {
  position: relative !important;
  display: block !important;
  height: auto !important;
  width: 100% !important;
  color: inherit !important;
  background: none !important;
}

a {
  color: $action-color !important;
  transition: all ease-in-out .15s;

  &:hover {
    filter: brightness(1.2);
  }
}

.bg-bg1 {
  background-color: transparent !important;
}

/// SCROLL BAR PROPERTIES ////////////// 

/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #aaa;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: #aaa;
}

*::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  border: 3px solid #aaa;
}

.selected .checkbox-button::before, 
.checkbox-button.selected::before, 
.custom-selected 
.checkbox-button::before {
  border-bottom: 4px solid $action-color;
  border-right: 4px solid $action-color;

}

// GENERAL MODUALS ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.oobtn {
  transition: all ease-in-out .15s;

  &.oobtn-primary {
    background-color: $action-color;
    border-radius: 4px;
    color: $text-primary-color;

    background-image: linear-gradient(to right, $action-color, $action-color-dark);
  }

  &.oobtn-secondary {
    border: 1px solid $action-color;
    border-radius: 4px;
    color: $action-color;
    font-weight: bold;

    padding-left: 25px;
    padding-right: 25px;
  }

  &:hover {
    filter: brightness(1.2);
  }
}

.btn-minus, .btn-plus, .oobtn-icon {
  transition: all ease-in-out .15s;
  cursor: pointer;
  color: $action-color;

  &:hover {
    filter: brightness(1.2);
  }
}

input, textarea, .combobox, select {
  
  box-sizing: border-box !important;
  // font-family: 'gil-sans-light', sans-serif !important;
  // background: rgba(0, 0, 0, .1) !important;

  // FIX: campos no se alinean bien en producción, falta width de los input
  width: 100%;

  & option {
      // background: rgba(0, 0, 0, .9);
      // color: #fff;
      // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
  }
}


.logo-container {
  background: url("images/logo.png") center center;
  background-repeat: no-repeat;
  background-size: contain;

  min-height: 150px;
  width: 100%;
  max-width: 350px; 

  filter: drop-shadow(2px 4px 12px #000000);
}


// prefered no background as default
#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
    background: transparent;
    overflow-y: auto;
    height: 100dvh;
    width: 100%;
    box-sizing: border-box !important;
}

.modal_window {
  color: $text-primary-color;
  border: 1px solid rgba(255, 255, 255, 0.33) !important;
  background-color: rgba(0,0,0,.9);
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22) !important;
  border-radius: 8px;
}

// .footer-m {
//   background: linear-gradient(to right bottom, rgba(0,0,0,.4),rgba(0,0,0,.3)) !important;
// }

.footer-m-embed, .footer-m {
  height: auto;
  padding: 8px;
}

.spin-loader {
  border: 3px solid $action-color !important
}

.stepper .dot.selected {
  background-color: $text-inverted-color;
}

.stepper .dot.selected::before {
  border: 2px solid $text-inverted-color;
}

.selected .radio-button::before, .custom-selected .radio-button::before {
  background-color: $action-color;
}

.button-close  {
  &.cta {
    color: $action-color !important;
    background: transparent !important;
  }
}

input:focus,
textarea:focus {
  border-color: $action-color !important;
  outline-color: $action-color !important;
  outline: 0 !important;
}

.wrap-col {
  flex-wrap: wrap;
}

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }
.justify-items-start { justify-items: flex-start; }
.justify-items-center { justify-items: center; }
.justify-items-end { justify-items: flex-end; }
.justify-self-start { justify-self: flex-start; }
.justify-self-center { justify-self: center; }
.justify-self-end { justify-self: flex-end; }

.align-content-start { align-content: flex-start; }
.align-content-center { align-content: center; }
.align-content-end { align-content: flex-end; }
.align-items-start { align-items: flex-start; }
.align-items-center { align-items: center; }
.align-items-end { align-items: flex-end; }
.align-self-start { align-self: flex-start; }
.align-self-center { align-self: center; }
.align-self-end { align-self: flex-end; }



// TABLE PROPERTIES ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#product-table-scroller {
  scroll-behavior: smooth;
  padding-bottom: 100px;
}

.cell {
  border: none !important;
}

.hero {
  background: none;
  color: $text-primary-color;
}

.photo-tmpl .product-image {
  margin: 8px 8px 0 8px;
  border-radius: 8px !important;
  background-color: #ffffff;
  background-repeat: no-repeat;
  height: auto;
  min-height: 235px;
  background-size: 106% !important;

  border: 1px solid lightgray;
} 


.mobile .product-table,
.desktop .product-table { 

  display: grid !important;
  grid-template: auto / auto;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  padding-left: 8px;
  padding-right: 8px;

  .section-header {
    background-color: $bg1;
  }

  .menu-section-header,
  .menu-section-title {
    grid-column: 1 / -1 !important;
    box-sizing: border-box;
    border-radius: $border-radius;
    color: $text-primary-color;
    // border-top: 1px solid rgba(255, 255, 255, 0.33);
      // background: none;
      // font-family: 'Great Vibes', cursive !important;
      // font-size: 220%;
      // text-align: center;
      // // border-top: 3px double #363636;
      // // border-bottom: 3px double #363636;
      // margin-top: 12px;
      // margin-bottom: 12px;
  }

  // .menu-section-title:before,
  // .menu-section-title:after {
  //     content: "";
  //     display: block;
  //     width: 100%;
  //     height: 20px;
  //     background: url( 'images/divider.svg' ) no-repeat center center;
  //     background-size: contain
  // }

  .cell {
    width: 100% !important;
    margin: 0 !important;
    background-color: rgba(255,255,255,.5);
    color: $text-primary-color;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);

    &>div {
        // border-radius: 1px solid $text-primary-color;
        // outline: 5px solid $bg2;
    }

    .hero {
      border-radius: 0 !important;
      margin-top: 0 !important;
      background-color: transparent !important;
      color: $text-primary-color;
    }
  
      // .image-cover {
      //     background-size: 105% auto !important;
      // }
      
      
  }

  
}

// make cells not rounded
.cell.rounded {
  // border-radius: 0 !important;
  // overflow: hidden;
}

.desktop #product-list-view > .product-table > .cell.product {
  // width: 233px !important;
  // min-width: 220px;
  // // flex: 1;
  // border-radius: 0px !important;
}




// PICKUP OR DELIVERY /////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#pickup-or-delivery 
{
  .pickup-icon, 
  .take-away-icon,
  .table-icon {
    width: 150px;
    height: 150px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    border: 1px solid rgba(255,255,255,.6);
    border-radius: 50%;
    padding: 16px;
    background-size: 70% !important;
    background-color: rgba(0,0,0,.1);
  }

  .take-away-icon {
    background-image: url(images/take_away.png) !important;
  }
  .pickup-icon {
    background-image: url(images/pickup.png) !important;
  }
  .table-icon {
    background-image: url(images/table.png) !important;
  }

  .take-away-block {}
  .pickup-block {}
  .table-block {}


  
  .grid-col {
    display: grid;
    grid-template-rows: 1fr auto;
    flex-wrap: nowrap;
  }
}



// CHOOSE MENU VIEW ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.desktop .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr) 360px;
  padding-left: 8px;
  padding-right: 8px;

  .ticket-view-wrapper {
    grid-row: 1/3;
    grid-column: 2/3;
  }
}

.mobile .menu-view-grid-container {
  display: grid;
  gap: 4px;
  grid-template-columns: minmax(0, 1fr);
  padding: 4px 4px 0 4px;
}




/////////////////// CATEGORY MENU VIEW //////////////////////////

#category-menu-view {
  position: relative !important;;

  font-size: 13px;
  letter-spacing: .05em;
  text-transform: uppercase;
  font-weight: 400;
  border-radius: 0;

  .glass-panel {
    border-radius: 3px !important;
  }

  .scroll-row {
    position: relative !important;
  }

  .menu-item {

    position: relative !important;

      &:hover,
      &.selected {

      }
  }

  .oobtn-link {
      color: #ffffff;
      font-weight: bold;
  }

  .oobtn-next, .oobtn-prev {
    position: relative !important;
    
    display: flex;
    align-items: center;
    
  }

    

  .oobtn-next:hover:before, .oobtn-prev:hover:before, .oobtn-link:hover {
      opacity: 1 !important;
      filter: brightness(1.2);
  }


  .btn-minus.disabled, .btn-plus.disabled, .oobtn-icon.disabled {
      color: #aaaaaa;
  }
}



/////////////////// PRODUCT LIST VIEW //////////////////////////

#product-add-view {
  height: auto !important;
}

.photo-tmpl.selected {
  border: 0 !important;

  &.glass-panel::after {
    background: linear-gradient(to right bottom, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.40)) !important; 
  }
}

/////////////////// TICKET VIEW //////////////////////////

.desktop #ticket-view {
  width: 100%;
  position: relative !important;
  top: inherit !important;
}

#checkout-date-picker-view {
  height: auto !important;
}

.custom-stripe > .stripped:nth-child(2n),
.stripe > div:nth-child(2n) {
  background-color: $stripe-color;
}


// CHECK OUT VIEW /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

#checkout-view.desktop .main-view-wrapper {
  padding-right: 8px;
}


#client-info-view,
#conditions-container,
#terms-wrapper {
    background-color: transparent !important;
    border-radius: 0px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    margin-top: 15px;
    color: $text-primary-color;

    .cell .hero {
        background-color: transparent !important;
        border-radius: 0 !important;
    }
}

.tab.selected {
  border-color: $action-color;
  color: $text-inverted-color;
}

#terms-wrapper {
  margin-left: 10px !important;
  margin-right: 10px !important;
  background-color: rgba(0, 0, 0, 0.7);
  border: 0;
  margin-bottom: 15px;
}

#payment-modal-view {
  
}


// STYLE CLASSES //////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.glass-panel {

  /* From https://css.glass */
  // background: rgba(255, 255, 255, 0.15) !important;
  position: relative;
  background: none !important;
  // background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
  // border-radius: .8em !important;
  // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
  // backdrop-filter: blur(5.3px) !important;
  // -webkit-backdrop-filter: blur(5.3px) !important;
  // border: 1px solid rgba(255, 255, 255, 0.33) !important;

  color: #ffffff !important;

  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;

    background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
    border-radius: .8em !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(5.3px) !important;
    -webkit-backdrop-filter: blur(5.3px) !important;
    border: 1px solid rgba(255, 255, 255, 0.33) !important;
  }
}

// ANIMATIONS /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.fade-in {
  animation: fadeIn ease-in-out .4s;

  &.slow { animation-duration: 1.2s; }
  &.medium { animation-duration: .8s; }
  &.fast { animation-duration: .4s; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}









.tmpl-img #choose-menu-view.mobile .main-view-wrapper {
    margin: 4px !important;
}


.desktop .photo-tmpl {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    transition: all 0.3s cubic-bezier(.25,.8,.25,1) !important;
}

.desktop .photo-tmpl:hover {
    will-change: inherit !important;
    -webkit-transform: none !important;
    transform: none !important;
    -webkit-animation-timing-function: inherit !important;
    animation-timing-function: inherit !important;

    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23) !important;
}



.ui-navigation-controller > div {
  position: relative !important;
  background: none !important;
}












