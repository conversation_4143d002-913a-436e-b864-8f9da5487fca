
@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Ultralight.woff') format('woff');
  font-weight: 100;
  font-style: normal;
}


@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Thin.woff') format('woff');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
}


@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Heavy.woff') format('woff');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'SF';
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.eot');
  src: url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.eot?#iefix') format('embedded-opentype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.ttf') format('truetype'),
       url('fonts/SF-UI/SF-UI-Display/SFUIDisplay-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
}


$font-family: "SF","Roboto", sans-serif ;
// #00BEBE (light)   (dark)
$action-color: #DD1317 ;
$bg1: #FFFFFF ; // #F9F4EE ;
$bg2: #FAFAFA ;
$fg1: #4b3918 ;
$stripe-color: #fAfAfA ;

$border-color: #D0D0DB ;
$text-primary-color: #1A1A1A ;
$text-secondary-color: #AFAFBD ;
$text-inverted-color: #FFFFFF ;

$price-color: #77a464 ;
