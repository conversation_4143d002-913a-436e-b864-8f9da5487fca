<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>ProductListView</title>
    <link href="{{CSS_URL}}" rel="stylesheet" />
</head>
<body>
    <div id="digital-menu-product-list-view" class="oorow">

        <!-- spin loader-->
        <div class="spin-loader-container" data-outlet="loading-spinning">
            <div class="spin-loader"></div>
        </div>

        <!-- grid table -->
        <div class="uitable product-table" data-outlet="table-view">

            <!-- HEADER -->
            <div class="menu-section-title big-title section-header" data-cell-identifier="sectionHeader" data-class="HeaderCell">
                <div class="title" data-outlet="title-lbl"><span>Header name</span></div>
            </div>

            <!-- CELL-->
            <div class="cell product" data-cell-identifier="ProductCell" data-class="ProductListCell">
                <!-- 1. product image -->
                <div class="img product-image" data-outlet="product-img"></div>
                <div class="product-cell-info-container">
                    <div class="row">
                        <!-- 2. product name -->
                        <div class="label product-title" data-outlet="product-name-lbl"><span>Example of Product Title</span></div>
                        <!-- 3. product price -->
                        <div class="product-price" data-outlet="product-price-lbl"><span>00.00€</span></div>
                    </div>
                    <!-- 4. formats (if product has FORMATS then show container and instert format lines) -->
                    <div class="product-format-row-container" data-outlet="product-format-row-container" style="display: none;">
                        <!--duplicate the flowing row for each modifier-->
                        <div class="product-format-line" data-outlet="format-line">
                            <div class="label" data-outlet="format-lbl">Format name</div>
                            <div class="label" data-outlet="format-value-lbl">+00.00€</div>
                        </div>
                    </div>
                    <!-- 5. product desc -->
                    <div cl ass="product-desc p-regular" data-outlet="product-desc-lbl">
                        Example Description: Lorem ipsum, dolor sit amet consectetur adipisicing elit. Consequatur nihil obcaecati sed sapiente labore necessitatibus.
                    </div>
                    <!-- 6. allergies -->
                    <div class="product-allergies oorow shrinkable">
                        <div class="allergens celery"      data-outlet="allergens-celery-img"     ></div>
                        <div class="allergens crustaceans" data-outlet="allergens-crustaceans-img"></div>
                        <div class="allergens dairy"       data-outlet="allergens-dairy-img"      ></div>
                        <div class="allergens egg"         data-outlet="allergens-egg-img"        ></div>
                        <div class="allergens fish"        data-outlet="allergens-fish-img"       ></div>
                        <div class="allergens hazelnut"    data-outlet="allergens-hazelnut-img"   ></div>
                        <div class="allergens lupins"      data-outlet="allergens-lupins-img"     ></div>
                        <div class="allergens mollusk"     data-outlet="allergens-mollusk-img"    ></div>
                        <div class="allergens mustard"     data-outlet="allergens-mustard-img"    ></div>
                        <div class="allergens peanut"      data-outlet="allergens-peanut-img"     ></div>
                        <div class="allergens sesame"      data-outlet="allergens-sesame-img"     ></div>
                        <div class="allergens soya"        data-outlet="allergens-soya-img"       ></div>
                        <div class="allergens sulphites"   data-outlet="allergens-sulphites-img"  ></div>
                        <div class="allergens wheat"       data-outlet="allergens-wheat-img"      ></div>
                    </div>
                </div>
            </div><!--end of cell-->
            
        </div><!--End TableView-->
        
    </div><!--End Product List View-->
</body>
</html>