<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//ES" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
	<title>Dual Link | App TPV en iPad para restaurantes</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<!--[if gte mso 9]>
		<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
		</xml>
	<![endif]-->
	<!--next two lines prevent ios from automatically creating links-->
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="telephone=no">
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<style type="text/CSS">
		body { padding: 0; margin: 0; }
		.ok_button {
			background-color: #c30e2d;
			padding: 12px 48px 12px;
			border: none;
			font-size: 18px;
			color: #ffffff;
		}
		.shield {
			display: block;
		}
		.legal-alert {
			position: relative;
			padding: 15px;
		}
		.legal-alert .shield {
			height: 108px;
			left: 0;
			position: absolute;
			top: 50px;
			width: 100%;
			z-index: 10;
		}
		@media (max-width: 800px) {}
		@media (max-width: 600px) {}
		@media (max-width: 400px) {}
	</style>
	<script>
		function enviar_formulario() {
			document.frm.submit();
			window.close();
		}
		function activateButton() {
			if (document.getElementById('legal-alert').checked == true) {
				document.getElementById("shield").style.display = 'none';
				document.getElementById("ssv_continue").style.opacity = '1';
			} else {
				document.getElementById("shield").style.display = 'block';
				document.getElementById("ssv_continue").style.opacity = '0.5';
			}
		}
		function desactivateButton() {
			setTimeout(function () {
				window.close();
			}, 500);
		}
	</script>
</head>
<body style="padding: 0; margin: 0;">

	<!--BULLET-PROOF BACKGROUND (BEGINING)-->
	<div style="background-color:#00bebe; height:100%;">
		<!--[if gte mso 9]>
		<v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
		<v:fill type="frame" color="#00bebe"/>
		</v	:background>
		<![endif]-->

	<!--START FULL PAGE TABLE WRAPPER-->
	<table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#00bebe" role="presentation" style="margin: 0; padding: 0; width: 100%; background: #00bebe; background: linear-gradient(145deg, rgba(22,187,187,1) 0%, rgba(15,122,168,1) 46%, rgba(15,122,168,1) 66%, rgba(0,212,255,1) 100%);" >
		<tr>
			<td></td>
			<td align="center" valign="top" width="600" style="max-width: 584px; padding: 0 8px;"><!-- max-size="600px" -->

				<!--START INNER CONTENT TABLE-->
				<table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%; color:#333333; font-size: 14px; font-family: Arial, Helvetica, sans-serif; line-height: 26px;" >
					
					<!--SPACER-->
					<tr style="height: 25px;"><td></td></tr>
					
					<!--HEADER ROW (Logo)-->
					<tr>
						<td valign="center" cellpadding="25" style="text-align: center;">
							<img src="img/logo-white.png" alt="Dual Link" style="width: 80%">
							<!--http://www.resources.dual-link.com/dual-link/_templates_2023/img/logo-white.png-->
						</td>
					</tr>

					<!--SPACER-->
					<tr style="height: 25px;"><td></td></tr>

					<!--BODY ROW-->
					<tr>
						<td colspan="2" cellpadding="25" style="border-top: 15px solid #00bebe; background-color: #ffffff; padding: 15px 25px; border-radius: 16px; box-shadow: 1px 1px 7px 0px #333333;">
							
							<p style="color: #777777; font-size: 32px; font-weight: bold; text-align: center;">Resumen de la reserva</p>

							<table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%; margin: 0 auto; color:#333333; font-size: 14px; font-family: Arial, Helvetica, sans-serif; line-height: 26px;" >
								<tr>
									<td>Evento:</td>
									<td>{{$eventName}}</td>
								</tr>
								<tr>
									<td>Zona:</td>
									<td>
										<span style="color: #c74741;font-size: 20px;text-align: left;">{{$zoneName}}</span>
									</td>
								</tr>
								<tr>
									<td>Número de personas:</td>
									<td>{{$pax}}</td>
								</tr>
								<tr>
									<td>Fecha:</td>
									<td>{{$bookingDate}}</td>
								</tr>
								<tr>
									<td>Nombre:</td>
									<td>{{$name}}</td>
								</tr>
								<tr>
									<td>Precio de la Reserva:</td>
									<td>{{$amount}} €</td>
								</tr>
								<tr>
									<td>Importe a cargar:</td>
									<td>{{$amountToPay}} €</td>
								</tr>
								<!--SPACER-->
								<tr style="height: 25px;"><td></td></tr>
							</table>

						</td>
					</tr>

					<!--SPACER-->
					<tr style="height: 25px;"><td></td></tr>

					<!--FOOTER ROW-->
					<tr>
						<td colspan="2" cellpadding="25" style="border-bottom: 15px solid #00bebe; background-color: #ffffff; padding: 15px 25px; border-radius: 16px; box-shadow: 1px 1px 7px 0px #333333; text-align: center;">
							<p>
								(IVA incluido)
							</p>
							<p>
								En el siguiente paso se carga en la tarjeta de credito el 50% de la reserva.
								<br />El importante restante se abonara el dia del evento reservado.
							</p>
							<p>
								$$form$$
							</p>
						</td>
					</tr>

					<!--SPACER-->
					<tr style="height: 3px;"><td></td></tr>

					<tr>
						<td style="color: #dddddd; text-align: center; font-size: 13px;">
							<a target="_blank" href="https://dual-link.com" style=" text-decoration:none; color:#dddddd;">www.dual-link.com</a> | <a target="_blank" href="https://dual-link.com/legal/politica-privacidad/" style=" text-decoration:none; color:#dddddd;">Política de privacidad</a> | <a target="_blank" href="https://dual-link.com/contacto/" style=" text-decoration:none; color:#dddddd;">Contacto</a>
						</td>
					</tr>

					<!--SPACER-->
					<tr style="height: 20px"></tr>

				</table><!--END INNER CONTENT TABLE-->

			</td>
			<td></td>
		</tr>
	</table><!--END FULL PAGE TABLE WRAPPER-->
	</div><!--BULLET-PROOF BACKGROUND (ENDING)-->
</body>
</html>