///////////////////////////////////////////////////////////
//   ____              _         _     _       _         //
//  |  _ \ _   _  __ _| |       | |   (_)_ __ | | __     //
//  | | | | | | |/ _` | |  ___  | |   | | '_ \| |/ /     //
//  | |_| | |_| | (_| | | |___| | |___| | | | |   <      //
//  |____/ \__,_|\__,_|_|       |_____|_|_| |_|_|\_\     //
//                                                       //
//	DUAL-LINK ONLINE MENU                                //
//  Web: www.dual-link.com                               //
//  Contact: <EMAIL>                          //
//                                                       //
//  © 2024 Dual Link Distribution S.L.                   //
//                                                       //
///////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////
//     http://meyerweb.com/eric/tools/css/reset/         //
//     v2.0 | 20110126                                   //
//     License: none (public domain)                     //
///////////////////////////////////////////////////////////

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}

html {
	box-sizing: border-box;
}

*, *:before, *:after {
	box-sizing: inherit;
}

body {
	line-height: 1;

	//this fixes the text rendering problems with Mac devices
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: subpixel-antialiased;
}

ol, ul {
	list-style: none;
}

blockquote, q {
	quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

*, *:before, *:after {
	transition: all 200ms ease;
}


///////////////////////////////////////////////////////////
// FONTS                                                 //
// Note: add personal fonts for venue here               //
///////////////////////////////////////////////////////////

.open-sans-thin {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 100;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-extralight {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 200;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-light {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 300;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-normal {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-medium {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 500;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-semibold {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 600;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-bold {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 700;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-semiblack {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 800;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.open-sans-black {
	font-family: "Open Sans", sans-serif;
	font-optical-sizing: auto;
	font-weight: 900;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}



/////// 1. VARIABLES ////////////////////////////////////////////////////////////

$brand-color-1: rgb(0,170,170);
$brand-color-2: rgb(27,120,167);
$brand-color-3: rgb(97,93,123);

$font-family: "Open Sans", sans-serif;
$font-family2: "Open Sans", sans-serif;

$action-color: #00AAAA;
$action-color-active: #008690;

$danger-color: #ff5544;
$safe-color: #59e28e;
$disabled-color: #bcbcbc;

$border-radius: 4px;
$spacing-general: 8px;
$window-max-width: 1024px;
$today-selector-color: #c73d31;

//// WINDOW & BACKGROUND
$window-bg-color: white;
$window-bg-image: none;
// $window-bg-image: url("../images/bg1.jpg");

//// STEP NUMBER
$step-action-color: $action-color;

//// TEXT
$text-font-family: 'Open Sans', sans-serif;
$text-font-size: 16px;
$text-font-weight: 400;
$text-primary-color: #222222;
$text-inverse-color: #ffffff;
$text-grey-color: #7e7e7e; //should be a lighter grey color

//// TITLES
$title-font-family: $text-font-family;
$title-font-size: 20px;
$title-font-weight: 700; 
$title-primary-color: #222222;
$title-inverse-color: #ffffff;
$title-text-transform: uppercase;

//// LINKS
$link-color: $action-color;
$link-color-hover: $action-color-active;
$link-text-decoration: none;

//// SEPARATOR
$separator-color: $action-color;

//// ICONS
$icon-color: $action-color;
$icon-color-hover: $action-color-active;
$icon-size: 24px; //width & height

//// BUTTONS
$button-primary-bg: $action-color;
$button-primary-bg-hover: $action-color-active;
$button-primary-color: $text-inverse-color;
$button-primary-color-hover: $text-inverse-color;
$button-primary-border: inherit;

$button-secondary-bg: inherit;
$button-secondary-bg-hover: inherit;
$button-secondary-color: $action-color;
$button-secondary-color-hover: $action-color-active;
$button-secondary-border: 2px solid $action-color;

$button-font-family: $text-font-family;
$button-font-size: $text-font-size;
$button-font-weight: 400;
$button-padding: $spacing-general $spacing-general*2;
//border radius added with @mixin

//// INPUTS ( includes textfields, checkbox, radio btn, text areas)
$input-active-color: $action-color;
$input-bg-color: white;
$input-border: 1px solid #444444;
$input-border-active: black;
$input-text-color: $text-primary-color;
$input-text-color-placeholder: $text-grey-color;
$input-padding: $spacing-general;
$input-font-family: $text-font-family;
$input-font-size: $text-font-size;

//// PANEL OPTIONS
$panel-bg-color: rgba(255, 255, 255, 0.75);
$panel-bg-blur: blur(3px);
$panel-border: 1px solid rgba(50, 50, 50, .1);
$panel-border-radius: $border-radius;
//border-radius and shadow are added with @mixin

//// TABLEVIEW
$tableview-stripe-color: #f5f5f5;

//// COLLECTION VIEW
$collectionview-rows: 4;

//// ALERT WINDOW
$alert-window-bg: inherit;
$alert-window-border: 1px solid #222222;
$alert-window-color: inherit;
//border radius added with @mixin

//// PRICE TAG (for DLOnlineOrders)
$price-text-color: initial;
$price-bg-color: initial;
$price-border: 1px solid rgba(0, 0, 0, 0.2);

//// TOP BAR (for DLOnlineOrders)
$top-bar-bg: #444444;
$top-bar-border: inherit;
$top-bar-text-color: $text-inverse-color;
//border radius added with @mixin

//// NAV BAR
$nav-bar-bg: #555555;
$nav-bar-border: inherit;
$nav-bar-text-color: inherit;


/////// 2. MIXINS ////////////////////////////////////////////////////////////

@mixin text-nowrap {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.text-nowrap {
    @include text-nowrap();
}

@mixin border-radius {
    border-radius: $border-radius;
}

.border-radius {
    @include border-radius();
}

@mixin shadow-low {
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.shadow-low {
    @include shadow-low();
}

@mixin shadow-high {
    box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.shadow-high {
    @include shadow-high();
}

@mixin bg-image {
    background-image: $window-bg-image;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
}

.bg-image {
    @include bg-image();
}



// ANIMATIONS /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.fade-in {
	animation: fadeIn ease-in-out .4s;

	&.slow { animation-duration: 1.2s; }
	&.medium { animation-duration: .8s; }
	&.fast { animation-duration: .4s; }
}

@keyframes fadeIn {
	0% { opacity: 0; }
	100% { opacity: 1; }
}




/////// 3. ICONS ////////////////////////////////////////////////////////////

// IMPORT FONTS FOR ICONS //

@font-face {
    font-family: "feather";
    src: url('../fonts/feather/feather.eot');                               /* IE9*/
    src: url('../fonts/feather/feather.eot') format('embedded-opentype'),   /* IE6-IE8 */
        url('../fonts/feather/feather.woff') format('woff'),                /* chrome, firefox */
        url('../fonts/feather/feather.ttf') format('truetype');             /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
        // url('../fonts/feather.svg') format('svg');                       /* iOS 4.1- */
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'ALLERGENS';
    src: url('../fonts/allergens/ALLERGENS.eot');
    src: url('../fonts/allergens/ALLERGENS.eot?#iefix') format('embedded-opentype'),
        url('../fonts/allergens/ALLERGENS.woff') format('woff'),
        url('../fonts/allergens/ALLERGENS.ttf') format('truetype'),
        url('../fonts/allergens/ALLERGENS.svg#ALLERGENS') format('svg');
    font-weight: normal;
    font-style: normal;
}


.icon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'feather' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    speak: none;

    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat;

    &.nav {
        background-image: url("../images/icons/manager-icon-menu.png");
        width: 20px;
        height: 20px;
    }

    &.left-bar {
        background-image: url("../images/icons/manager-icon-show-left.png");
        width: 26px;
        height: 20px;
    }

    &.right-bar {
        background-image: url("../images/icons/manager-icon-show-right.png");
        width: 26px;
        height: 20px;
    }

    &.category {
        @include border-radius;
        background-color: $action-color;
        width: 10px;
        height: 10px;
    }
    
    &.arrow {
        border: solid $action-color;
        border-width: 0 3px 3px 0;
        display: inline-block;
        padding: 3px;

        &.right {
            transform: rotate(-45deg);
            -webkit-transform: rotate(-45deg);
        }
        
        &.left {
            transform: rotate(135deg);
            -webkit-transform: rotate(135deg);
        }
        
        &.up {
            transform: rotate(-135deg);
            -webkit-transform: rotate(-135deg);
        }
        
        &.down {
            transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
        }
    }

    &.allergens {
        /* use !important to prevent issues with browser extensions that change fonts */
        font-family: 'ALLERGENS' !important;
        width: 15px;
        height: 15px;
        font-size: 15px;
        margin-right: 4px;
        color: #d3d3d3;

        &.wheat:before {
            content: "\0047";
            color: #f47039;
            font-weight: normal;
        }

        &.crustaceans:before {
            content: "\0044";
            color: #1cb6f1;
            font-weight: normal;
        }

        &.egg:before {
            content: "\0049";
            color: #f68d2f;
            font-weight: normal;
        }

        &.fish:before {
            content: "\004c";
            color: #28429d;
            font-weight: normal;
        }

        &.peanut:before {
            content: "\0042";
            color: #c27344;
            font-weight: normal;
        }

        &.dairy:before {
            content: "\004a";
            color: #6d351e;
            font-weight: normal;
        }

        &.hazelnut:before {
            content: "\0046";
            color: #da4752;
            font-weight: normal;
        }

        &.celery:before {
            content: "\0043";
            color: #54bf36;
            font-weight: normal;
        }

        &.mustard:before {
            content: "\004e";
            color: #c09328;
            font-weight: normal;
        }

        &.sesame:before {
            content: "\0048";
            color: #9a8e6c;
            font-weight: normal;
        }

        &.lupins:before {
            content: "\0041";
            color: #fadd3d;
            font-weight: normal;
        }

        &.mollusk:before {
            content: "\004b";
            color: #4dc4d5;
            font-weight: normal;
        }

        &.soya:before {
            content: "\004d";
            color: #02a75b;
            font-weight: normal;
        }

        &.sulphites:before {
            content: "\0045";
            color: #82104e;
            font-weight: normal;
        }
    }

}


/////// 3. STRUCTURE ////////////////////////////////////////////////////////////


// UTILITY CLASSES //

.hidden { display: none !important; }
.invisible { visibility: hidden; }
  
a,
a:visited {
	color: $link-color;
	text-decoration: $link-text-decoration;

	&:hover {
		color: $link-color-hover;
	}
}
  
.hr {
	border: 1px solid $separator-color;
}
  
.sticky-top {
	position: sticky;
	top: 0;
}
  
.grid-container {
	display: grid;
}

.flex-row {
	display: flex;
	flex-direction: row;
}

.flex-col {
	display: flex;
	flex-direction: column;
}

.grid-row {
	display: grid;
	grid-template-rows: 1fr;
	grid-template-columns: auto;
	grid-auto-columns: auto;
}

.grid-col {
	display: grid;
	grid-template-rows: auto;
	grid-template-columns: 1fr;
	grid-auto-rows: auto;
}

.grid-container, 
.flex-row, 
.flex-col, 
.grid-row, 
.grid-col {
	&.gap { gap: $spacing-general; }
	&.gap-2 { gap: $spacing-general * 2; }
	&.gap-3 { gap: $spacing-general * 3; }
	&.gap-4 { gap: $spacing-general * 4 ; }
}
  
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-full { grid-column: 1/-1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-5 { grid-row: span 5; }

.align-content-start { align-content: flex-start; }
.align-content-center { align-content: center; }
.align-content-end { align-content: flex-end; }
.align-self-start { align-self: flex-start; }
.align-self-center { align-self: center; }
.align-self-end { align-self: flex-end; }
.align-items-start { align-items: flex-start; }
.align-items-center { align-items: center; }
.align-items-end { align-items: flex-end; }

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }
.justify-self-start { justify-self: flex-start; }
.justify-self-center { justify-self: center; }
.justify-self-end { justify-self: flex-end; }
.justify-items-start { justify-items: flex-start; }
.justify-items-center { justify-items: center; }
.justify-items-end { justify-items: flex-end; }

.text-align-left { text-align: left; }
.text-align-center { text-align: center; }
.text-align-right { text-align: right; }

  
// TITLES AND TEXT //
  
  .title, .label {
	&.danger { color: $danger-color; }
	&.safe { color: $safe-color; }
	&.disable { color: $disabled-color; }
	&.grey { color: $text-grey-color; }
  
	&.small { font-size: 85%; }
	&.large { font-size: 115%; }
	&.italic { font-style: italic; }
	&.bold { font-weight: 700; }
  }
  
  .title {
	font-weight: $title-font-weight;
	font-size: $title-font-size;
	font-family: $title-font-family;
	color: $title-primary-color;
	text-transform: $title-text-transform;
  }
  
  .label {
	font-family: $text-font-family;
	font-size: $text-font-size;
	font-weight: $text-font-weight;
	color: $text-primary-color;
  }
  
  
  
// IMAGES //
  
  .img {
	@include border-radius;
	background-color: #eeeeee;
	min-width: 100px;
	min-height: 100px;
  }
  
  
  
// STRUCTURE //
  
  html {
	scrollbar-color: #888 #aaa;
	scrollbar-width: thin;
  }
  
  body {
	position: relative;
	top: 0;
	left: 0;
	width: 100%;
  
	font-family: $text-font-family;
	color: $text-primary-color;
  }
  
  .window {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100dvh;
	z-index: 0;
  
	display: grid;
	align-items: center;
  
	overflow-y: auto;  //this makes the scroll window the "window" container
  }
  
  .window:first-child {
	@include bg-image;  //background image goes in window container
	
	background-color: $window-bg-color;
  }
  
  .window:not(:first-child) {
	backdrop-filter: blur(6px);
	background-color: rgba(0, 0, 0, .3);
  }
  
  .view {
	position: relative;
	top: 0;
	left: 0;
	width: 100%;
	height: auto;
  }
  
  .root-container {
	position: relative;
	display: grid;
	// gap: $spacing-general * 3;
	max-width: $window-max-width;
	padding: $spacing-general * 2;
	margin: 0 auto;  //center the container
  
	&.desktop {}
	&.mobile {}
  }
  
  
/////// 10. FORM ELEMENTS ////////////////////////////////////////////////////////////

/////// BUTTONS ////////////////////////////////////////////////////////////

.btn {
	@include border-radius;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
	user-select: none;
	padding: $button-padding;
	font-weight: $button-font-weight;
	font-family: $button-font-family;
	font-size: $button-font-size;
	cursor: pointer;
  
	&.primary {
	  @include shadow-low;
	  background-color: $button-primary-bg;
	  color: $button-primary-color;
	  border: $button-primary-border;
  
	  width: min-content;
	  padding: $spacing-general ($spacing-general * 4);
  
	  * {
		color: $button-primary-color;
	  }
  
	  &:hover {
		background-color: $button-primary-bg-hover;
		color: $button-primary-color-hover;
	  }
  
	  &:disabled,
	  &.disabled {
		background-color: $disabled-color;
		color:  lighten($disabled-color, 30%);
		border-color: $disabled-color;
		opacity: .7;
  
		.label {
		  color:  lighten($disabled-color, 30%);
		}
	  }
	}
  
	&.secondary {
	  @include shadow-low;
	}
  
	&.secondary,
	&.link {
	  background-color: $button-secondary-bg;
	  color: $button-secondary-color;
	  border: $button-secondary-border;
  
	  &:hover {
		background-color: $button-secondary-bg-hover;
		color: $button-secondary-color-hover;
	  }
  
	  &:disabled,
	  &.disabled {
		background-color: lighten($disabled-color, 30%);
		color: $disabled-color;
		border-color: $disabled-color;
		opacity: .7;
		
		.label {
		  color: $disabled-color;
		}
	  }
	}
  
	&.minus,
	&.plus,
	&.icon {
	  font-family: 'feather' !important;
	  color: $icon-color;
	  font-size: $icon-size;
	  width: $icon-size;
	  height: $icon-size;
	  background-size: contain;
	  background-repeat: no-repeat;
  
	  &:disabled,
	  &.disabled {
		color: $disabled-color;
		opacity: .7;
	  }
	}
  
	&.minus:before {
	  content: "\e894";
	}
  
	&.minus.no-circle:before {
	  content: "\e897";
	}
  
	&.plus:before {
	  content: "\e8b0";
	}
  
	&.plus.no-circle:before {
	  content: "\e8b1";
	}
  
	&.next:before {
	  content: "\e844";
	}
  
	&.prev:before {
	  content: "\e843";
	}
}
  
  
  
/////// FORM AND FORM ELEMENTS ////////////////////////////////////////////////////////////
  
.form-block {
	display: grid;
	grid-template-columns: 1fr;
	grid-template-rows: auto;
	grid-auto-rows: auto;
	gap: 4px;
}
  
  
  
  /////// INPUTS ////////////////////////////////////////////////////////////
  
  select,
  input,
  textarea {
	@include border-radius;
	font-size: $text-font-size;
	font-family: $text-font-family;
	border: $input-border;
	padding: calc($spacing-general/2) $spacing-general;
	background-color: $input-bg-color;
	width: 100%;
	color: $input-text-color;
	letter-spacing: -.5px;
	line-height: 1.2;
	height: 100%;
  
	transition: 200ms all ease;
  
	&:focus {
	  outline: none;
	  border-color: $action-color;
	}
  
	&:disabled,
	&.disabled {
	  background-color: lighten($disabled-color, 50%);
	  color: $disabled-color;
	  border-color: $disabled-color;
	  opacity: .7;
	}
  }
  
  textarea {
	resize: vertical;
  }
  
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
  }
  
  /* Firefox */
  input[type=number] {
	-moz-appearance: textfield;
  }
  
  // Safari fix: selects are show different in Safari
  input,
  select {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
  }
  
  .input-combobox {
	position: relative;
  
	// add arrow to select
	&::before {
	  content: '';
	  position: absolute;
	  top: 50%;
	  right: 0;
	  transform: translate(-50%, -50%);
	  width: 0;
	  height: 0;
	  border-left: 5px solid transparent;
	  border-right: 5px solid transparent;
	  border-top: 8px solid $action-color;
	  clear: both;
	  pointer-events: none;
	}
  
}
  
  
//// RAIDIO BUTTON /////
  
  .radio-button {
	border: $input-border;
	border-radius: 50%;
	width: 14px;
	height: 14px;
	position: relative;
	box-sizing: content-box;
  }
  
  .radio-button.selected::before {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 50%;
	height: 7px;
	width: 7px;
	background-color: $input-active-color;
  }
  
  //// CHECKBOX BUTTON ////
  
  .checkbox-button {
	border: $input-border;
	border-radius: $border-radius;
	width: 14px;
	height: 14px;
	min-width: 14px;
	position: relative;
	box-sizing: content-box;
  }
  
  .checkbox-button.selected::before {
	content: "";
	position: absolute;
	top: -5%;
	left: 20%;
	transform: rotate(45deg);
	height: 9px;
	width: 4px;
	border-bottom: 4px solid $action-color;
	border-right: 4px solid $action-color;
  }
  

  
/////// 11. SPIN LOADER - ACTIVITY INDICATOR  ////////////////////////////////////////////////////////////

.spin-loader {
    border: 3px solid $action-color;
    border-right-color: transparent;
    border-radius: 50%;
    width: 20px;
    height: 20px;
  
    position: relative;
    // right: $spacing-general;
    // top: $spacing-general;
    z-index: 10;
  
    animation: rotate 0.8s infinite linear;
}

.btn .spin-loader {
  border-color: $text-inverse-color;
  border-right-color: transparent;
}
  
@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



/////// 12. STEP NUMBER ////////////////////////////////////////////////////////////

.step-title-container {
	position: relative;
	display: grid;
	grid-template-columns: auto 1fr;
	gap: $spacing-general;

	left: -($spacing-general * 3);
}

.step-number {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 30px;
	height: 30px;
	background-color: $disabled-color;
	color: #ffffff;
	border-radius: $border-radius;
	font-size: 130%;

	&.selected {
		background-color: $step-action-color;
	}
}



/////// 13. SEGMENTED STYLE  ////////////////////////////////////////////////////////////

.segmented-style {
	display: grid;
	grid-auto-flow: column;

	div {
		display: flex;
		box-sizing: border-box;
		justify-content: center;
		align-items: center;
		text-align: center;
		cursor: pointer;

		padding: 8px;

		&.selected {
		@include border-radius;
		border: 0;
		background-color: $action-color;
		color: $text-inverse-color;
		}

		&:hover:not(.selected) {
		opacity: .8;
		}
	}
}



/////// 14. TOP BAR ////////////////////////////////////////////////////////////

.top-bar {
    @include shadow-low;
    @include border-radius;
  
    position: sticky;
    top: $spacing-general;
    left: 0;
    z-index: 10;
  
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: $spacing-general;
    
    align-items: center;
    justify-content: center;
    padding: $spacing-general;
    //add left & right margin so shadows are not cut short by overflow:auto
    margin-left: $spacing-general;
    margin-right: $spacing-general;
  
    background-color: $top-bar-bg;
  
    .title,
    .label {
        @include text-nowrap;
        color: $top-bar-text-color;
    }
  
}



/////// 15. SEARCH BAR ////////////////////////////////////////////////////////////

.search-bar {
    input {}
}



//// 16. PANEL OPTIONS ////////////////////////////////////////////////////////////

@mixin bg-panel {
	@include shadow-low();

	background-color: $panel-bg-color;
	backdrop-filter: $panel-bg-blur;
	-webkit-backdrop-filter: $panel-bg-blur;
	border: $panel-border;
	border-radius: $panel-border-radius;

	padding: $spacing-general * 2;
	transition: 200ms all ease;
}
  
.bg-panel {
	@include bg-panel();
}
  
  
//// INDENT PANEL OPTIONS
/// used for adding an indent in the above panel, good for adding new info points

@mixin bg-indent-panel {
	border-radius: $border-radius;
	background-color: rgba($color: #000000, $alpha: .03);
	border: 1px solid #ffffff;
	border-top: 1px solid #dddddd;
	border-left: 1px solid #dddddd;
	padding: $spacing-general;
	backdrop-filter: blur(3px);
	-webkit-backdrop-filter: blur(3px);
	transition: 200ms all ease;

	//added to make indent window fill space and align better with logo
	flex: 1;
	align-self: stretch;
	justify-content: center;
}
  
.bg-indent-panel {
	@include bg-indent-panel();
}
  
  
//// GLASS PANEL
/// alternate predefined glass panel

@mixin glass-panel {
	/* From https://css.glass */
	// background: rgba(255, 255, 255, 0.15) !important;
	position: relative;
	background: none !important;
	color: #ffffff !important;

	&::after {
		content: "";
		display: block;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: -1;

		background: linear-gradient(to right bottom, rgba(255, 255, 255, .3), rgba(255, 255, 255, .15)) !important;
		border-radius: .8em !important;
		box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
		backdrop-filter: blur(5.3px) !important;
		-webkit-backdrop-filter: blur(5.3px) !important;
		border: 1px solid rgba(255, 255, 255, 0.33) !important;
	}
}
  
.glass-panel {
	@include glass-panel();
}



/////// 17. LANGUAGE CHANGER - LANGUAGE COMBO BOX ////////////////////////////////////////////////////////////

.language-container {
    display: block;
    position: fixed;
    z-index: 10;
    top: 8px;
    right: 16px;
    padding: 8px;
  
    select {
      padding-right: 16px;
    }
}



/////// 20. CALENDAR VIEW ////////////////////////////////////////////////////////////

#calendar-view {

	.calendar-header {
  
	  .calendar-nav {
		position: relative;
		display: grid;
		grid-template-columns: 20px auto auto 20px;
		gap: $spacing-general;
		width: 100%;
		height: auto;
		padding: $spacing-general 0;
  
		.calendar-prev-btn,
		.calendar-next-btn {
		  display: inline-flex;
		  align-items: center;
		  justify-content: center;
		  cursor: pointer;
		  width: 20px;
	  
		  &:hover {
			opacity: .8;
		  }
		}
  
		// create an arrow with css border
		.calendar-prev-btn:before,
		.calendar-next-btn:before {
		  content: "";
		  border: solid $action-color;
		  border-width: 0 3px 3px 0;
		  display: inline-block;
		  padding: 3px;
		}
  
		//rotate arrow to face the left
		.calendar-prev-btn:before {
		  transform: rotate(135deg);
		  -webkit-transform: rotate(135deg);
		}
  
		//rotate arrow to face the right
		.calendar-next-btn:before {
		  transform: rotate(-45deg);
		  -webkit-transform: rotate(-45deg);
		}
  
	  }
  
	  .input-combobox {
		width: auto;
	  }
  
	}
  
  
	.ui-calendar-body {
  
	  table.calendar-days-view {
		width: 100%;
		border-spacing: 0;
		border-collapse: collapse;
  
		tr {
		  display: grid;
		  grid-template-columns: repeat(7, 1fr);
		}
	  
		>thead {
		  border-bottom: 2px solid $action-color;
  
		  >tr>th {
			padding: $spacing-general 0;
			font-weight: bold;
		  }
		}
  
		>tbody {
		  display: grid;
		  grid-template-rows: auto;
  
		  >tr>td {
			position: relative;
			text-align: center;
			padding: ($spacing-general*1.5) 0;
			cursor: pointer;
  
			&.disabled {
			  // class "disabled" is currently added to all days regardless, can't use until fixed
			  // color: lighten($text-primary-color, 50);
			  // cursor: initial !important;
			}
  
			&.empty-cell {
			  // add background color to empty cells
			}
  
			&.selected {
			  background-color: $action-color;
			  color: $text-inverse-color;
			  border-radius: $border-radius;
			}
  
			&.day-cell.day-6:not(.selected),
			&.day-cell.day-0:not(.selected) {
			  background-color: rgba($color: $action-color, $alpha: .2);
			}
  
			&.day-cell.today:before {
			  content: "";
			  position: absolute;
			  display: block;
			  pointer-events: none;
		
			  // create a circle to represent "today"
			  border-radius: 55% 47% 40% 47%;
			  border-width: 1px 3px 4px 3px;
			  transform: rotate(20deg);
			  top: 2px;
			  left: 2px;
			  right: 2px;
			  bottom: 1px;
			  border-style: solid;
			  border-color: $today-selector-color;
			}
  
			&.day-cell.today:after {
			  content: "";
			  position: absolute;
			  display: block;
			  pointer-events: none;
  
			  // create another circle to represent "today"
			  border-radius: 10px 15px 12px 15px;
			  border-width: 3px 0 3px 0;
			  transform: rotate(-40deg);
			  top: 5px;
			  left: 5px;
			  right: 4px;
			  bottom: 3px;
			  border-style: solid;
			  border-color: $today-selector-color;
			}
		  }
		} //end of tbody
	  }//end of table
	}
  
}


/////// 21. TABLEVIEW AND COLLECTION VIEW ////////////////////////////////////////////////////////////

//wrap this class around a table view to control the scollable area
.table-scroller {
	position: relative;
	scroll-behavior: smooth;
	overflow-y: auto;
	flex: 1;
  
	//if table-view then add properties to table-scroller
	&:has(.table-view) {
	  @include border-radius;
	  margin-top: $spacing-general;
	  margin-right: $spacing-general;
	  margin-left: $spacing-general;
	  margin-bottom: $spacing-general;
	}
  
	//if collection-view then add properties to table-scroller
	&:has(.collection-view) {
	  //add left & right padding so shadows are not cut short by overflow-y:auto
	  padding-top: $spacing-general;
	  padding-right: $spacing-general;
	  padding-left: $spacing-general;
	}
  
	.collection-view,
	.table-view {
	  position: relative;
	  display: grid;
	  grid-template: auto / auto;
  
	  /* NOTE: this sets the row to 100% width, look for variable solution in base css styles */
	  // 
	  align-content: flex-start;
  
	  .cell {
		padding: $spacing-general;
		gap: $spacing-general;
		cursor: pointer;
  
		&.header {
		  grid-column: 1 / -1;
		  cursor: initial;
		}
	  }
	}
  
	//TableView specific adjustments
	.table-view {
	  grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
	  gap: 0;
  
	  //add a light stripe to every other item in table
	  &>div:nth-child(2n) {
		background-color: $tableview-stripe-color;
	  }
  
	  .cell {
		//NOTE: additional cell properties specified in app specific style sheet
		// for example: grid-template-columns is definded according to apps needs
		display: flex;
		align-items: center;
		justify-content: flex-start;
		border-radius: 0; 
	  }
	}
  
	//CollectionView specific adjustments
	.collection-view {
	  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
	  gap: $spacing-general;
	  margin-bottom: $spacing-general;
  
	  .cell {
		//NOTE: additional cell properties specified in app specific style sheet
		// for example: grid-template-columns is definded according to apps needs
		@include border-radius;
		display: grid;
		align-items: center;
		justify-content: flex-start;
		//grid-template-columns: 1f; //this is defined in app specific style sheet
	  }
	}
  
}



///////////////////////////////////////////////////////////////
//  22. TAGS CONTAINER ////////////////////////////////////////////
///////////////////////////////////////////////////////////////

.tags-container {
	position: relative;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 8px;

	align-items: center;
	background-color: transparent;
	box-sizing: border-box;
	padding: 8px;
	margin: 0 auto;
	max-width: 800px;

	.tag {
		color: $text-primary-color;
		font-size: 80%;
		font-weight: 200;

		cursor: pointer;
		
		// margin: 0 0 0 8px;
		// &:last-child { margin-right: 8px; }
	}
}


///////////////////////////////////////////////////////////////
//  23. LEFT NAV WINDOW ///////////////////////////////////////
///////////////////////////////////////////////////////////////


$nav-window-bg: white;

.side-window {
	position: absolute;
	top: 0;
	display: flex;
	flex-direction: column;
	height: 100dvh;
	max-width: 90vw;
	min-width: 35vw;
	padding: 8px;
	z-index: 20;
	
	background-color: $nav-window-bg;
	color: $text-primary-color;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  
	transition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);

	&.left {
		left: 0;
		transform: translateX(-110%);
	}

	&.right{
		right: 0;
		transform: translateX(110%);
	}
  
	&.show-window {
		transform: translateX(0);
	}
  
	.button-wrapper {
		min-height: 50px;
		display: flex;
		justify-content: flex-end;
	}
  
}
  



/////// 100. ONLINE ORDERS ////////////////////////////////////////////////////////////


.price-tag {
    @include border-radius;
    padding: $spacing-general;
    background-color: $price-bg-color;
    color: $price-text-color;
  }


///////////////////////////////////////////////////////////
//                                                       //
//	OnlineOrdersChooseMenuView for Digital Orders        //
//                                                       //
///////////////////////////////////////////////////////////


#online-orders-choose-menu-view {

	#order-choose-menu-view-container {
		grid-template-columns: auto 1fr auto;
	}

	.nav-bar-wrapper {
		background-color: $nav-bar-bg;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
	}

	#nav-bar {
		display: grid;
		grid-template-columns: 1fr auto 1fr;
		max-width: 800px;
		margin: 0 auto;
	  
		.logo-container {
			background-position: center center;
			background-repeat: no-repeat;
			background-size: contain;
			transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);

			background-image: url("images/logo.png");

			width: 100%;
			min-width: 200px;
			max-width: 350px;
			min-height: 120px;

			&.reduce-logo { min-height: 40px; }
		}  
	  }
	  
	  //styling for hamburger button
	  .hamburger {
		display: flex;
		align-items: center;
		position: relative;
		height: 26px;
		padding: 12px;
		cursor: pointer;
	  
		.main-nav-toggle {
			display: block;
			position: relative;
			width: 28px;
			height: 16px;
	  
			&:after,
			&:before {
				content: '';
				position: absolute;
				top: 0;
				height: 0;
				border-bottom: 4px solid $action-color;
				width: 100%;
				left: 0;
				right: 0;
				// transition: all ease-out 0.3s;
				transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
			}
	  
			&:after {
				top: 100%;
			}
	  
			i {
				display: block;
				text-indent: 100%;
				overflow: hidden;
				white-space: nowrap;
				height: 4px;
				background-color: $action-color;
				width: 100%;
				position: absolute;
				top: 50%;
				// transition: all ease-out 0.1s;
				transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
			}
	  
			&.active-menu {
				&:after {
					transform: rotate(-45deg);
					transform-origin: center;
					top: 50%;
				}
	  
				&:before {
					transform: rotate(45deg);
					transform-origin: center;
					top: 50%
				}
	  
				i {
					opacity: 0;
				}
			}
		}
	}


	.category-list-wrapper {
		min-width: 120px;

		.category-list {}
	}


	.ticket-view-wrapper {
		min-width: 120px;

		.ticket-view {}
	}




}



///////////////////////////////////////////////////////////
//                                                       //
//	OrderProductMenuView for Digital Orders              //
//                                                       //
///////////////////////////////////////////////////////////


#order-product-menu-view {}



///////////////////////////////////////////////////////////
//                                                       //
//	OrderProductSummaryView for Digital Orders           //
//                                                       //
///////////////////////////////////////////////////////////


#order-product-summary-view {}



///////////////////////////////////////////////////////////
//                                                       //
//	OrderTypeLandingView for Digital Orders              //
//                                                       //
///////////////////////////////////////////////////////////


#order-type-landing-view {}



///////////////////////////////////////////////////////////
//                                                       //
//	OrderNoticeScreenView for Digital Orders             //
//                                                       //
///////////////////////////////////////////////////////////


#order-notice-screen-view {}


  
/////// 110. ONLINE BOOKINGS ////////////////////////////////////////////////////////////

/////// 111. ONLINE BOOKINGS CALENDAR FORM ////////////////////////////////////////////////////////////

/////// 112. ONLINE BOOKINGS RESTAURANT FORM ////////////////////////////////////////////////////////////

/////// 113. ONLINE BOOKINGS CLIENT FORM ////////////////////////////////////////////////////////////



/////// 120. DIGITAL MENU ////////////////////////////////////////////////////////////



///////////////////////////////////////////////////////////
//                                                       //
//	MenuRootView_Desktop for Digital Menus               //
//                                                       //
///////////////////////////////////////////////////////////


#menu-root-view-desktop {}



///////////////////////////////////////////////////////////
//                                                       //
//	MenuRootView_Mobile for Digital Menus               //
//                                                       //
///////////////////////////////////////////////////////////


#menu-root-view-mobile {}



///////////////////////////////////////////////////////////
//                                                       //
//	MenuCategoryListView for Digital Menus               //
//                                                       //
///////////////////////////////////////////////////////////


#menu-category-list-view {

    .cell {
        // MENU CELL - PHOTO
        &.menu-category {}
    }
}



///////////////////////////////////////////////////////////
//                                                       //
//	MenuProductListView for Digital Menus                //
//                                                       //
///////////////////////////////////////////////////////////


#menu-product-list-view {   

    .cell {
        // MENU CELL - PHOTO
        &.menu-photo-cell {
            display: grid;
            grid-template: auto 1fr auto / auto 1fr auto;
            .img { grid-row: span 3; }
            .description,
            .product-icon-container { grid-column: span 2; }
        }

        // MENU CELL - NO PHOTO
        &.menu-no-photo-cell {
            display: grid;
            grid-template: auto 1fr auto / 1fr auto;
            .description,
            .product-icon-container { grid-column: span 2; }
        }
    }
}



///////////////////////////////////////////////////////////
//                                                       //
//	MenuNoticeScreenView for Digital Menus          //
//                                                       //
///////////////////////////////////////////////////////////


#menu-notice-screen-view {}




/////// 130. DIGITAL GIFTS ////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////
//                                                       //
//	GiftProductDetailView for Digital Menus              //
//                                                       //
///////////////////////////////////////////////////////////


#gift-product-detail-view {

    .gift-product-detail-container {
        grid-template-columns: 1fr 1fr;
        gap: $spacing-general * 4;
        padding-bottom: $spacing-general * 4;

        .menu-nav-container {
            grid-template-columns: $icon-size 1fr $icon-size;
        }

        .product-img-container {
            position: relative; //so that the label can be absoutly positioned

            .product-img {
                width: 100%;
                height: 100%;
            }

            .label {
                position: absolute;
                bottom: $spacing-general;
                left: $spacing-general;
            }
        }

        .product-info-container {}
        .recipient-info-container {}
        .client-info-container {}

        .client-review-submit-container {
            grid-template-columns: 1fr 1fr;
        }

    }

}



/////// 140. RETAIL??? ////////////////////////////////////////////////////////////

