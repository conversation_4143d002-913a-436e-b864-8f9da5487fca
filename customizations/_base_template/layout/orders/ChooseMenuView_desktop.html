<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>OnlineOrdersChooseMenuView_desktop</title>
    <link href="../styles.css" rel="stylesheet" />
    <link rel="stylesheet" href="../../deploy/app.css">
  </head>
  <body>
    <div id="online-orders-choose-menu-view" class="view" data-root-view-controller="true" data-class="OnlineOrdersChooseMenuViewController">

      <!--Holds all elements on screen-->
      <div id="order-choose-menu-view-container" class="desktop grid-container gap-3">

        <!-- CUSTOMIZABLE HEADER, added from "templates/bookings/addons/BookingHeaderView.html"-->
        <!-- {{HEADER}} -->

        
        <!--LEFT SIDE - SLIDE IN WINDOW with category list (absolute positioned)-->
        <div class="side-window left grid-container gap" id="side-window-left">

          <!--navigational buttons in nav window-->
          <div class="button-wrapper">
            <div class="hamburger align-self-end" data-outlet="hamburger-close-btn">
              <div class="main-nav-toggle active-menu">
                <i>x close</i>
              </div>
            </div>
          </div>
          
          <!--category list navigation wrapper-->
          <div class="category-list-wrapper" data-outlet="category-list-view"></div>
        </div>


        <!--RIGHT SIDE - SLIDE IN WINDOW with category list (absolute positioned)-->
        <div class="side-window right grid-container gap" id="side-window-right">

          <!--navigational buttons in nav window-->
          <div class="button-wrapper">
            <div class="hamburger align-self-start" data-outlet="cart-close-btn">
              <div class="main-nav-toggle active-menu">
                <i>x close</i>
              </div>
            </div>
          </div>
          
          <!--ticket view -->
          <div class="ticket-view-wrapper" data-outlet="ticket-view"></div>
        </div>


        <!--TOP BAR NAVIGATION-->
        <div class="nav-bar-wrapper col-span-3">
          
          <div id="nav-bar" class="nav-bar grid-container gap align-items-center">
            
            <!--ICON-->
            <div class="hamburger justify-self-start" data-outlet="hamburger-btn">
              <div class="main-nav-toggle">
                <i>Menu</i>
              </div>
            </div>

            <!--LOGO-->
            <div id="logo-container" class="logo-container"></div>
            
            <!--ICON-->
            <div class="hamburger justify-self-end" data-outlet="cart-btn">
              <div class="main-nav-toggle">
                <i>Menu</i>
              </div>
            </div>
  
          </div>
        </div>


        <!--CATEGORY LIST CONTAINER-->
        <div class="category-list-wrapper">
          <div class="category-list" data-outlet="category-list-view"></div>
        </div>


        <!--PRODUCT LIST wrapper (page scrolls on this div)-->
        <div id="product-table-scroller" class="flex-1 fade-in medium" style="overflow: auto;">

          <!--SEARCH BAR - scrolls with produts-->
          <div class="search-bar" data-outlet="search-bar">
            <input class="icon-search oobtn-icon" type="search" placeholder="Search" data-placeholder="SEARCH">
            <i class="oobtn-icon icon-search"></i>
          </div>

          <!--TAGS CONTAINER-->
          <div class="tags-container content card" data-outlet="tag-view" style="display: none;">
            <div class="label tag"><span data-localized-key="TAG">TAG</span></div>
          </div>

          <!--PRODUCT LIST VIEW inserted into wrapper-->
          <div class="main-view-wrapper flex-1" data-outlet="product-list-view"></div>

        </div>


        <!--ticket view -->
        <div class="ticket-view-wrapper">
          <div class="ticket-view" data-outlet="ticket-view"></div>
        </div>



      </div> <!--end container-->

    </div>
</body>
</html>
