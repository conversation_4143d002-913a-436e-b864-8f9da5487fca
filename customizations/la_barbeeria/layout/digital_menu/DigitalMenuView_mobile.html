<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>DigitalMenuView Mobile</title>
    <link href="../styles.css" rel="stylesheet" />
  </head>
  <body>
    <div id="digital-menu-view" class="root-screen mobile">

      <!--Holds all elements on screen-->
      <div class="menu-view-grid-container">
  
        <!--SLIDE IN WINDOW with category list (absolute positioned)-->
        <div class="nav-window" id="nav-window" data-outlet="side-window-left">

          <!--navigational buttons in nav window-->
          <div class="button-wrapper">
            <div class="hamburger align-self-end justify-self-start" data-outlet="hamburger-close-btn">
              <div class="main-nav-toggle active-menu">
                <i>x close</i>
              </div>
            </div>
          </div>

          <!--category list navigation wrapper-->
          <div class="category-wrapper" data-outlet="category-list-view"></div>
        </div>

        <!--TOP BAR NAVIGATION-->
        <div class="nav-bar-wrapper">
          <div id="nav-bar" class="nav-bar fade-in fast">
            <!--ICON-->
            <div class="hamburger justify-self-start" data-outlet="hamburger-btn">
              <div class="main-nav-toggle">
                <i>Menu</i>
              </div>
            </div>
            <!--LOGO-->
            <div id="logo-container" class="logo-container"></div>
            <!--blank div to sit in right side space-->
            <div class="blank"></div>
  
          </div>
        </div>

        <!--PRODUCT LIST wrapper (page scrolls on this div)-->
        <div id="product-table-scroller" class="flex-1 fade-in medium" style="overflow: auto;">

          <!--SEARCH BAR - scrolls with produts-->
          <div class="search-bar" data-outlet="search-bar">
            <input class="icon-search oobtn-icon" type="search" placeholder="Search" data-placeholder="SEARCH">
            <i class="oobtn-icon icon-search"></i>
          </div>

          <!--TAGS CONTAINER-->
          <div class="tags-container content card" data-outlet="tag-view" style="display: none;">
            <div class="label tag"><span data-localized-key="TAG">TAG</span></div>
          </div>

          <!--PRODUCT LIST VIEW inserted into wrapper-->
          <div class="main-view-wrapper flex-1" data-outlet="product-list-view"></div>

        </div>

      </div> 
        
    </div>
</body>
</html>