html {
  scrollbar-color: #888 #aaa;
  scrollbar-width: thin;
  font-size: 18px !important;
  font-weight: 400 !important;
}

.page {
  background: url(images/achnitzel-table-home.png) center center no-repeat;
  background-size: cover;
  overflow-y: scroll;
}

input, textarea, .combobox, select {
  color: #aaaaaa !important;
  width: 100%;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

table.calendar-days-view > tbody > tr > td.day-cell.day-6:not(.selected) > div,
table.calendar-days-view > tbody > tr > td.day-cell.day-0:not(.selected) > div {
  background-color: #222222 !important;
}

table.calendar-days-view > tbody > tr > td.selected > div,
.time-range-cell.selected {
  background-color: rgba(96, 196, 183, 0.8) !important;
}

button {
  text-transform: uppercase !important;
  color: #ffffff !important;
  border-radius: 0 !important;
}

.oobtn-primary {
  color: #ffffff !important;
  border-radius: 0 !important;
}
.oobtn-primary:disabled, .oobtn-primary.disabled {
  background-color: #410000;
  color: #777777 !important;
  cursor: not-allowed;
}

.step-number {
  color: #0e0d0a !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.cell.rounded {
  border-radius: 0 !important;
}

.segmented-style div.selected {
  color: #0e0d0a !important;
  background-color: rgb(195, 45, 48) !important;
}

form#booking-form {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 15px;
}

.ui-navigation-controller > div {
  position: relative !important;
  top: inherit !important;
  bottom: inherit !important;
  left: inherit !important;
  right: inherit !important;
}

#ticket-view,
#category-menu-view {
  background-color: rgba(0, 0, 0, 0.7);
}

#category-menu-view {
  font-size: 13px;
  color: #ffffff;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  line-height: 60px;
  font-weight: 500;
}
#category-menu-view .oobtn-link {
  color: #ffffff;
}
#category-menu-view .btn-minus, #category-menu-view .btn-plus, #category-menu-view .oobtn-icon {
  height: auto;
  color: #ffffff;
}
#category-menu-view .btn-minus, #category-menu-view .btn-plus, #category-menu-view .oobtn-icon.disabled {
  color: #aaaaaa;
}

.mobile .product-table .menu-section-title,
.desktop .product-table .menu-section-title {
  background-color: rgba(0, 0, 0, 0.8);
}
.mobile .product-table .cell,
.desktop .product-table .cell {
  border: 2px solid #ffffff !important;
  background-color: rgba(0, 0, 0, 0.7);
}
.mobile .product-table .cell .hero,
.desktop .product-table .cell .hero {
  border-radius: 0 !important;
  color: #FFFFFF;
  margin-top: 0 !important;
  background-color: transparent !important;
}
.mobile .product-table .cell .image-cover,
.desktop .product-table .cell .image-cover {
  background-size: 105% auto !important;
}
.mobile .product-table .cell .product-image,
.desktop .product-table .cell .product-image {
  border-radius: 0 !important;
}

.client-info-view .cell {
  background-color: rgba(0, 0, 0, 0.8);
}

.custom-stripe > .stripped:nth-child(2n), .stripe > div:nth-child(2n) {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.checkout-view-contitions-container {
  margin: 16px 0 0 0;
  padding: 15px;
  border: 2px solid #ffffff !important;
  background-color: rgba(0, 0, 0, 0.8);
}

.mobile .checkout-view-contitions-container {
  margin: 16px 10px;
}

.mobile .product-table {
  display: grid !important;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 12px;
}
.mobile .product-table .menu-section-title {
  grid-column: 1/span 2;
}
.mobile .product-table .cell {
  margin: 0 !important;
}
.mobile .product-table .cell .product-image {
  background-size: auto 105% !important;
}
.mobile .product-table .photo-tmpl .product-image {
  height: 150px;
}

.logo-view {
  display: block;
  background: url("images/logo-email.png") center center no-repeat;
  background-size: contain;
  margin: 0 auto;
  width: 100%;
  padding-top: 45.8%;
}/*# sourceMappingURL=theme.css.map */