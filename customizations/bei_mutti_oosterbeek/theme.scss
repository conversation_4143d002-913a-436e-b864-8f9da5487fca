////////////////////////////////////////////
// Theme for ____________________
// 
// NOTES:
// Iframe background color: 
// font-family: 
////////////////////////////////////////////

// add font files here and googleapi fonts in header.html
// @font-face {
//     font-family: 'gil-sans-light';
//     src:  url('fonts/5402Gil-Sans-light.woff2') format('woff2');
// }

$font-family: Jost, sans-serif;

$action-color: rgb(195, 45, 48);
$bg1: transparent;
$fg1: #ddd ;
$bg2: rgba(0, 0, 0, .7);
$stripe-color: #f5f5f5 ;

$border-color: #ffffff;
$text-primary-color: #989898;
$text-secondary-color: #eeeeee;
$text-inverted-color: #aaaaaa ;

$input-bordercolor: #e5e5e5 ;
$input-bg-color: rgba(255,255,255,.1);
$hover-color: lighten( $action-color, 40% ) ;

$shadow-color: #000000;
$danger-color: #b40303;
$curtain-color: #0009 ;

$price-color: #77a464;

iframe {}

html {
    //background-color:   // used for simulating bg of webpage iframe for testing
    scrollbar-color: #888 #aaa;
    scrollbar-width: thin;
    font-size: 18px !important;
    font-weight: 400 !important;
    box-sizing: border-box !important;
}

body {
    height: inherit;
}

.tmpl-img {
    position: relative;
    height: 100vh;
    background: url(images/achnitzel-table-home.png) center center no-repeat;
    background-size: cover;
}

// prefered no background as default
#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
    background: transparent;
    overflow-y: auto;
    height: 100vh;
    width: 100%;
    box-sizing: border-box !important;
}

.modal_window {
    background-color: rgba(0, 0, 0, 0.9) !important;
}


#ticket-view,
#category-menu-view {
    background-color: rgba(0, 0, 0, 0.7);
}

#terms-wrapper {
    background-color: rgba(0, 0, 0, 0.7);
    border: 2px solid #ffffff;
    margin-top: 16px;
}

// .main-view-wrapper {
//     background-color: rgba(0, 0, 0, 0.7);
// }


.bg-bg1 {} //use with solid color or nothing, shows up in wierd places

// .product-price,
// .modifier-price { color: $price-color !important; }

// styles for callendar and other inputs
input, textarea, .combobox, select {
    // font-family: 'gil-sans-light', sans-serif !important;
    // background: rgba(0, 0, 0, .1) !important;
    color: #aaaaaa !important;

    // FIX: campos no se alinean bien en producción, falta width de los input
    width: 100%;

    & option {
        // background: rgba(0, 0, 0, .9);
        // color: #fff;
        // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
    }
}

// Remove incremental arrows from Input (Number) /* Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

// Remove incremental arrows from Input (Number) /* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

table.calendar-days-view > tbody > tr > td.day-cell.day-6:not(.selected) > div,
table.calendar-days-view > tbody > tr > td.day-cell.day-0:not(.selected) > div {
    background-color: #222222 !important;
}

table.calendar-days-view > tbody > tr > td.selected > div,
.time-range-cell.selected {
    background-color: rgba(96, 196, 183, .8) !important;
}

// table.calendar-days-view > tbody > tr > td.disabled > div {
//     color: #7e7e7e !important;
// }

// .calendar-form-view .calendar-nav .calendar-prev-btn,
// .calendar-form-view .calendar-nav .calendar-next-btn {
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }

button {
    text-transform: uppercase !important;
    color: #ffffff !important;
    border-radius: 0 !important;
}

.oobtn-primary {
    // button styles
    color: #ffffff !important;
    border-radius: 0 !important;

    &:hover {
        // hover on button styles
    }

    &:disabled,
    &.disabled {
        // disabled button styles
        background-color: #410000;
        color: #777777 !important;
        cursor: not-allowed;
    }
}


.step-number {
    color: #0e0d0a !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

.cell.rounded {
    border-radius:0 !important;
}

.segmented-style div.selected {
    color: #0e0d0a !important;
    background-color: $action-color !important;
}

form#booking-form {
    background-color: rgba(0, 0, 0, .7);
    padding: 15px;
}

.ui-navigation-controller > div {
    //background image fix
    position: relative !important;
    top: inherit !important;
    bottom: inherit !important;
    left: inherit !important;
    right: inherit !important;
}


#category-menu-view {

    font-size: 13px;
    color: #ffffff;
    letter-spacing: .05em;
    text-transform: uppercase;
    line-height: 60px;
    font-weight: 500;

    .oobtn-link {
        color: #ffffff;
    }

    .btn-minus, .btn-plus, .oobtn-icon {
        height: auto;
        color: #ffffff;
    }

    .btn-minus, .btn-plus, .oobtn-icon.disabled {
        color: #aaaaaa;
    }
}

.mobile .product-table,
.desktop .product-table { 

    .menu-section-title {
        background-color: rgba(0, 0, 0, 0.8);
    }

    .cell {
        border: 2px solid #ffffff !important;
        background-color: rgba(0, 0, 0, 0.7);

        .hero {
            border-radius: 0 !important;
            color: #FFFFFF;
            margin-top: 0 !important;
            background-color: transparent !important;
        }
    
        .image-cover {
            background-size: 105% auto !important;
        }
        
        .product-image {
            border-radius: 0 !important;
            background-repeat: no-repeat;
        }
    }
}

.client-info-view .cell {
    background-color: rgba(0, 0, 0, 0.8);
}

.custom-stripe > .stripped:nth-child(2n), .stripe > div:nth-child(2n) {
    background-color: rgba(0,0,0,.5) !important;
}

.checkout-view-contitions-container {
    margin: 16px 0 0 0;
    padding: 15px;
    border: 2px solid #ffffff !important;
    background-color: rgba(0, 0, 0, 0.8);
}

.mobile .checkout-view-contitions-container {
    margin: 16px 10px;
}


#product-list-view {
    overflow-x: hidden;
    overflow-y: auto;
}

#product-add-view,
.product-add-view {
    background-color: rgba(0,0,0,.9);
}


//force two columns in mobile view
.mobile .product-table {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 12px;
    box-sizing: border-box;


    .menu-section-title {
        grid-column: 1 / span 2;
    }

    .cell {
        margin: 0 !important;

        .product-image {
            background-size: 109% auto !important;
        }
    }

    .photo-tmpl .product-image {
        height: 150px;
    }

}

.logo-view {
    display: block;
    background: url("images/logo-email.png") center center no-repeat;
    background-size: contain;
    margin: 0 auto;
    width: 100%;
    padding-top: 45.8%;
}

#pickup-or-delivery .pickup-icon
{
  background-image: url("images/pickup.png") !important;
}

#pickup-or-delivery .take-away-icon
{
  background-image: url("images/take_away.png") !important;
}

#pickup-or-delivery .table-icon
{
  background-image: url("images/table.png") !important;
}

#checkout-date-picker-view {
    background-color: #000000;
}

.footer-m {
    background-color: rgba(0,0,0,.4);
}

.photo-tmpl .btn-minus {
    background-color: #000000 !important;
}

.desktop #product-list-view > .product-table > .cell.product {
    width: 45% !important;
}

#product-table-scroller {
    scroll-behavior: smooth;
}