<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>DigitalMenuProductListView</title>
</head>
<body>
    <div id="digital-menu-product-list-view" class="oorow">

        <!-- spin loader-->
        <div class="spin-loader-container" data-outlet="loading-spinning">
            <div class="spin-loader"></div>
        </div>

        <!-- grid table -->
        <div class="uitable product-table" data-outlet="table-view">

            <!-- HEADER -->
            <div class="menu-section-title big-title section-header bg-panel" data-cell-identifier="sectionHeader" data-class="HeaderCell">
                <div class="flex-row">
                    <div class="title" data-outlet="title-lbl"><span>Header name</span></div>
                    <div class="title secondary" data-outlet="second-title-lbl"><span></span></div>
                </div>
            </div>

            <!-- NEW CELL -->
            <div class="cell product" data-cell-identifier="" data-class="" style="display: none;">
                
                <!--product info (horizontal alignment of photo, info, and price)-->
                <div class="product-grid">

                    <!--product image-->
                    <div class="img product-image image-cover" data-outlet="product-img"></div>
    
                    <!--product name-->
                    <div class="flex-col">
                        <div class="label product-title" data-outlet="product-name-lbl"><span>Example of Product Title</span></div>
                        <div class="label product-title secondary" data-outlet="product-alternate-name-lbl"><span></span></div>
                    </div>
                    
    
                    <!--product price-->
                    <div class="product-price" data-outlet="product-price-lbl"><span>00.00€</span></div>
    
                    <!--if product has FORMATS then show container and instert format lines-->
                    <div class="product-format-row-container" data-outlet="product-format-row-container" style="display: none;">
                        <!--duplicate the flowing row for each modifier-->
                        <div class="product-format-line" data-outlet="format-line">
                           <div class="label" data-outlet="format-lbl">Format name</div>
                           <div class="label" data-outlet="format-value-lbl">+00.00€</div>
                       </div>
                   </div>
    
                    <!--product desc-->
                    <div class="p-regular" data-outlet="product-desc-lbl">
                        Example Description: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.
                    </div>
    
                    <!--allergies-->
                    <div class="oorow shrinkable">
                        <div class="allergens celery"      data-outlet="allergens-celery-img"     ></div>
                        <div class="allergens crustaceans" data-outlet="allergens-crustaceans-img"></div>
                        <div class="allergens dairy"       data-outlet="allergens-dairy-img"      ></div>
                        <div class="allergens egg"         data-outlet="allergens-egg-img"        ></div>
                        <div class="allergens fish"        data-outlet="allergens-fish-img"       ></div>
                        <div class="allergens hazelnut"    data-outlet="allergens-hazelnut-img"   ></div>
                        <div class="allergens lupins"      data-outlet="allergens-lupins-img"     ></div>
                        <div class="allergens mollusk"     data-outlet="allergens-mollusk-img"    ></div>
                        <div class="allergens mustard"     data-outlet="allergens-mustard-img"    ></div>
                        <div class="allergens peanut"      data-outlet="allergens-peanut-img"     ></div>
                        <div class="allergens sesame"      data-outlet="allergens-sesame-img"     ></div>
                        <div class="allergens soya"        data-outlet="allergens-soya-img"       ></div>
                        <div class="allergens sulphites"   data-outlet="allergens-sulphites-img"  ></div>
                        <div class="allergens wheat"       data-outlet="allergens-wheat-img"      ></div>
                    </div>
                    
                </div>
            </div><!--end of cell-->
            

            <!-- CELL-->
            <div class="cell product bg-panel" data-cell-identifier="ProductCell" data-class="ProductListCell">
                
                <!--product info (horizontal alignment of photo, info, and price)-->
                <div class="grid-row"> 
                    
                    <!--product image-->
                    <div class="img product-image image-cover" data-outlet="product-img"></div>
                    
                    <div class="oocol gap">
                        <!--product name-->
                    <div class="flex-col">
                        <div class="label product-title" data-outlet="product-name-lbl"><span>Example of Product Title</span></div>
                        <div class="label product-title secondary" data-outlet="product-alternate-name-lbl"><span></span></div>
                    </div>
                        <!--product desc-->
                        <div class="p-regular" data-outlet="product-desc-lbl">
                            Example Description: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.
                        </div>
                        <!--allergies-->
                        <div class="oorow shrinkable">
                            <div class="allergens celery"      data-outlet="allergens-celery-img"     ></div>
                            <div class="allergens crustaceans" data-outlet="allergens-crustaceans-img"></div>
                            <div class="allergens dairy"       data-outlet="allergens-dairy-img"      ></div>
                            <div class="allergens egg"         data-outlet="allergens-egg-img"        ></div>
                            <div class="allergens fish"        data-outlet="allergens-fish-img"       ></div>
                            <div class="allergens hazelnut"    data-outlet="allergens-hazelnut-img"   ></div>
                            <div class="allergens lupins"      data-outlet="allergens-lupins-img"     ></div>
                            <div class="allergens mollusk"     data-outlet="allergens-mollusk-img"    ></div>
                            <div class="allergens mustard"     data-outlet="allergens-mustard-img"    ></div>
                            <div class="allergens peanut"      data-outlet="allergens-peanut-img"     ></div>
                            <div class="allergens sesame"      data-outlet="allergens-sesame-img"     ></div>
                            <div class="allergens soya"        data-outlet="allergens-soya-img"       ></div>
                            <div class="allergens sulphites"   data-outlet="allergens-sulphites-img"  ></div>
                            <div class="allergens wheat"       data-outlet="allergens-wheat-img"      ></div>
                        </div>
                    </div>

                    <!--product price-->
                    <div class="product-price" data-outlet="product-price-lbl"><span>00.00€</span></div>

                    <!--if product has FORMATS then show container and instert format lines-->
                    <div class="product-format-row-container" data-outlet="product-format-row-container" style="display: none;">
                         <!--duplicate the flowing row for each modifier-->
                         <div class="product-format-line" data-outlet="format-line">
                            <div class="label" data-outlet="format-lbl">Format name</div>
                            <div class="label" data-outlet="format-value-lbl">+00.00€</div>
                        </div>
                    </div>
                    
                </div>
            </div><!--end of cell-->


            <!-- CELL not used?-->
            <div class="photo-tmpl product cell rounded oocol glass-panel" data-cell-identifier="ProductBigPhotoCellDesktop" data-class="ProductListCell">
                <div class="oocol flex-1" data-outlet="product-plus-btn">
                  <div class="product-image oocol image-cover" data-outlet="product-img"></div>
                  <div class="hero oop-3 oocol flex-1">
                      <div class="oorow oomb-3 title-bold justify-content-center product-title" data-outlet="product-name-lbl">
                        <span>Tuna Sushi con Alga (8 piezas)</span>
                      </div>
                      <div class="flex-1 p-regular oomb-2" data-outlet="product-desc-lbl">
                          Rollo relleno con fresa, queso crema y rúcula, recubierto de
                          atún con salsa de chili dulce teriyaki.
                      </div>
                      <div class="oorow">
                          <div class="oorow shrinkable" style="max-width: 265px;">
                            <div class="allergens celery"      data-outlet="allergens-celery-img"     ></div>
                            <div class="allergens crustaceans" data-outlet="allergens-crustaceans-img"></div>
                            <div class="allergens dairy"       data-outlet="allergens-dairy-img"      ></div>
                            <div class="allergens egg"         data-outlet="allergens-egg-img"        ></div>
                            <div class="allergens fish"        data-outlet="allergens-fish-img"       ></div>
                            <div class="allergens hazelnut"    data-outlet="allergens-hazelnut-img"   ></div>
                            <div class="allergens lupins"      data-outlet="allergens-lupins-img"     ></div>
                            <div class="allergens mollusk"     data-outlet="allergens-mollusk-img"    ></div>
                            <div class="allergens mustard"     data-outlet="allergens-mustard-img"    ></div>
                            <div class="allergens peanut"      data-outlet="allergens-peanut-img"     ></div>
                            <div class="allergens sesame"      data-outlet="allergens-sesame-img"     ></div>
                            <div class="allergens soya"        data-outlet="allergens-soya-img"       ></div>
                            <div class="allergens sulphites"   data-outlet="allergens-sulphites-img"  ></div>
                            <div class="allergens wheat"       data-outlet="allergens-wheat-img"      ></div>
                          </div>
                          <div class="title-bold product-price flex-1 text-right" data-outlet="product-price-lbl">
                            <span>7.95€</span>
                          </div>
                      </div>
                  </div>
                </div>
            </div><!--end of cell-->
            
        </div><!--End TableView-->
        
    </div><!--End Product List View-->
</body>
</html>