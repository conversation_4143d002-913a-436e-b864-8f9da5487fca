@charset "UTF-8";
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before,
blockquote::before,
blockquote:after,
blockquote::after,
q:before,
q::before,
q:after,
q::after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

.roboto-light {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 200;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.roboto-regular {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.roboto-medium {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.roboto-bold {
  font-family: "Roboto", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

* {
  box-sizing: border-box;
}

html {
  box-sizing: border-box;
  background: none;
  min-height: 100dvh;
  font-weight: 400;
  font-family: "Roboto", sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #212529;
  scroll-behavior: smooth;
}

body {
  position: relative;
  display: block;
  height: auto;
  width: 100%;
  color: inherit;
  background: none;
}

a {
  color: #3e6c93;
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
  text-decoration: none;
}
a:hover {
  filter: brightness(1.2);
}

.bg-bg1 {
  background-color: transparent !important;
}

.gap {
  gap: 8px;
}

/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #aaa;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: #aaa;
}

*::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  border: 3px solid #aaa;
}

.selected .checkbox-button::before,
.checkbox-button.selected::before,
.custom-selected .checkbox-button::before {
  border-bottom: 4px solid #3e6c93;
  border-right: 4px solid #3e6c93;
}

.view,
.window {
  position: relative;
}

.oobtn {
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
}
.oobtn.oobtn-primary {
  background-color: #3e6c93;
  border-radius: 4px;
  color: #222222;
  background-image: linear-gradient(to right, #3e6c93, #C7899A);
}
.oobtn.oobtn-secondary {
  border: 1px solid #3e6c93;
  border-radius: 4px;
  color: #3e6c93;
  font-weight: bold;
  padding-left: 25px;
  padding-right: 25px;
}
.oobtn:hover {
  filter: brightness(1.2);
}

.btn-minus,
.btn-plus,
.oobtn-icon {
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
  cursor: pointer;
  color: #3e6c93;
  background: transparent !important;
}
.btn-minus:hover,
.btn-plus:hover,
.oobtn-icon:hover {
  filter: brightness(1.2);
}

input,
textarea,
.combobox,
select {
  box-sizing: border-box !important;
  width: 100%;
}
#pickup-or-delivery .logo-container {
  background: url("images/logo.png") center center;
  background-repeat: no-repeat;
  background-size: contain;
  min-height: 150px;
  width: 100%;
  max-width: 350px;
}

.logo-title-container {
  font-family: "Roboto", sans-serif;
  font-size: 140%;
  color: #222222;
}

#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
  background: transparent;
  overflow-y: auto;
  height: 100dvh;
  width: 100%;
  box-sizing: border-box !important;
}

.modal_window {
  color: #222222;
  border: 1px solid rgba(255, 255, 255, 0.33) !important;
  background-color: rgba(0, 0, 0, 0.9);
  box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22) !important;
  border-radius: 8px;
}

.footer-m-embed,
.footer-m {
  height: auto;
  padding: 8px;
}

.spin-loader {
  border: 3px solid #3e6c93 !important;
  border-right-color: transparent !important;
  width: 32px;
  height: 32px;
}

.stepper .dot.selected {
  background-color: #000000;
}

.stepper .dot.selected::before {
  border: 2px solid #000000;
}

.selected .radio-button::before,
.custom-selected .radio-button::before {
  background-color: #3e6c93;
}

.button-close.cta {
  color: #3e6c93 !important;
  background: transparent !important;
}

input:focus,
textarea:focus {
  border-color: #3e6c93 !important;
  outline-color: #3e6c93 !important;
  outline: 0 !important;
}

.flex-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.wrap-col {
  flex-wrap: wrap;
}

.col-span-2 {
  grid-column: span 2;
}

.col-span-3 {
  grid-column: span 3;
}

.col-span-4 {
  grid-column: span 4;
}

.col-span-5 {
  grid-column: span 5;
}

.col-span-full {
  grid-column: 1/-1;
}

.row-span-2 {
  grid-row: span 2;
}

.row-span-3 {
  grid-row: span 3;
}

.row-span-4 {
  grid-row: span 4;
}

.row-span-5 {
  grid-row: span 5;
}

.row-span-full {
  grid-row: 1/-1;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-items-start {
  justify-items: flex-start;
}

.justify-items-center {
  justify-items: center;
}

.justify-items-end {
  justify-items: flex-end;
}

.justify-self-start {
  justify-self: flex-start;
}

.justify-self-center {
  justify-self: center;
}

.justify-self-end {
  justify-self: flex-end;
}

.justify-space-between {
  justify-content: space-between;
}

.align-content-start {
  align-content: flex-start;
}

.align-content-center {
  align-content: center;
}

.align-content-end {
  align-content: flex-end;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-center {
  align-items: center;
}

.align-items-end {
  align-items: flex-end;
}

.align-self-start {
  align-self: flex-start;
}

.align-self-center {
  align-self: center;
}

.align-self-end {
  align-self: flex-end;
}

.text-align-left {
  text-align: left;
}

.text-align-center {
  text-align: center;
}

.text-align-right {
  text-align: right;
}

#product-table-scroller {
  scroll-behavior: smooth;
  padding-bottom: 100px;
}

.cell {
  border: none !important;
}

.hero {
  background: none;
  color: #222222;
}

.photo-tmpl .product-image {
  margin: 8px 8px 0 8px;
  border-radius: 8px !important;
  background-color: #ffffff;
  background-repeat: no-repeat;
  height: auto;
  min-height: 235px;
  background-size: 106% !important;
  border: 1px solid lightgray;
}

.mobile .product-table,
.desktop .product-table {
  display: grid !important;
  grid-template: auto/auto;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  padding-left: 8px;
  padding-right: 8px;
}
.mobile .product-table .section-header,
.desktop .product-table .section-header {
  background-color: #16103B;
  color: #3e6c93;
  background-color: #16103B;
  background: url("images/bg1.jpg") no-repeat;
  background-size: cover;
  box-shadow: 0 8px 10px rgba(0, 0, 0, 0.2), 0 6px 8px rgba(0, 0, 0, 0.3);
}
.mobile .product-table .section-header::after,
.desktop .product-table .section-header::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  background: linear-gradient(to right top, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05)) !important;
  border: 1px solid #464450;
  border-radius: 4px !important;
}
.mobile .product-table .menu-section-header,
.mobile .product-table .menu-section-title,
.desktop .product-table .menu-section-header,
.desktop .product-table .menu-section-title {
  grid-column: 1/-1 !important;
  box-sizing: border-box;
  border-radius: 0;
  color: #3e6c93;
  font-family: "Roboto", sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}
.mobile .product-table .cell,
.desktop .product-table .cell {
  width: 100% !important;
  margin: 0 !important;
  background-color: none;
  font-size: 14px;
  color: #222222;
}
.mobile .product-table .cell .hero,
.desktop .product-table .cell .hero {
  border-radius: 0 !important;
  margin-top: 0 !important;
  background-color: transparent !important;
  color: #222222;
}

#pickup-or-delivery .pickup-icon,
#pickup-or-delivery .take-away-icon,
#pickup-or-delivery .table-icon {
  width: 150px;
  height: 150px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  border: 1px solid #3e6c93;
  border-radius: 50%;
  padding: 16px;
  background-size: 70% !important;
}
#pickup-or-delivery .take-away-icon {
  background-image: url(images/take_away_dark.png) !important;
}
#pickup-or-delivery .pickup-icon {
  background-image: url(images/pickup_dark.png) !important;
}
#pickup-or-delivery .table-icon {
  background-image: url(images/table_dark.png) !important;
}
#pickup-or-delivery .grid-col {
  display: grid;
  grid-template-rows: 1fr auto;
  flex-wrap: nowrap;
}

.desktop .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr) 360px;
  padding-left: 8px;
  padding-right: 8px;
}
.desktop .menu-view-grid-container .ticket-view-wrapper {
  grid-row: 1/3;
  grid-column: 2/3;
}

.mobile .menu-view-grid-container {
  display: grid;
  gap: 4px;
  grid-template-columns: minmax(0, 1fr);
  padding: 4px 4px 0 4px;
}

#category-menu-view {
  position: relative !important;
  font-size: 13px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  font-weight: 400;
  border-radius: 0;
}
#category-menu-view .glass-panel {
  border-radius: 3px !important;
}
#category-menu-view .scroll-row {
  position: relative !important;
}
#category-menu-view .menu-item {
  position: relative !important;
}
#category-menu-view .oobtn-link {
  font-weight: bold;
}
#category-menu-view .oobtn-next,
#category-menu-view .oobtn-prev {
  position: relative !important;
  display: flex;
  align-items: center;
}
#category-menu-view .oobtn-next:hover:before,
#category-menu-view .oobtn-prev:hover:before,
#category-menu-view .oobtn-link:hover {
  opacity: 1 !important;
  filter: brightness(1.2);
}
#category-menu-view .btn-minus.disabled,
#category-menu-view .btn-plus.disabled,
#category-menu-view .oobtn-icon.disabled {
  color: #aaaaaa;
}

#product-add-view {
  height: auto !important;
}

.photo-tmpl.selected {
  border: 0 !important;
}
.photo-tmpl.selected.glass-panel::after {
  background: linear-gradient(to right top, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.4)) !important;
}

.desktop #ticket-view {
  width: 100%;
  position: relative !important;
  top: inherit !important;
}

.ticket-view-title {
  color: #3e6c93;
  font-weight: bold;
}

#checkout-date-picker-view {
  height: auto !important;
}

.custom-stripe > .stripped:nth-child(2n),
.stripe > div:nth-child(2n) {
  background-color: rgba(255, 255, 255, 0.2);
}

#checkout-view.desktop .main-view-wrapper {
  padding-right: 8px;
}

#client-info-view,
#conditions-container,
#terms-wrapper {
  background-color: transparent !important;
  border-radius: 0px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
  margin-top: 15px;
  color: #222222;
}
#client-info-view .cell .hero,
#conditions-container .cell .hero,
#terms-wrapper .cell .hero {
  background-color: transparent !important;
  border-radius: 0 !important;
}

.tab.selected {
  border-color: #3e6c93;
  color: #3e6c93;
}

#terms-wrapper {
  margin-left: 10px !important;
  margin-right: 10px !important;
  background-color: rgba(0, 0, 0, 0.7);
  border: 0;
  margin-bottom: 15px;
}

#digital-menu-view {
  background: transparent;
  overflow-y: auto;
  height: 100dvh;
  width: 100%;
  box-sizing: border-box !important;
}
#digital-menu-view .scroll-row {
  position: relative !important;
  overflow: auto;
}
#digital-menu-view .menu-view-grid-container {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
  gap: 0;
  padding: 0;
}
#digital-menu-view #rest_topbar {
  position: relative;
  background-color: #252525;
  border: 0;
  flex-direction: column;
  padding-top: 8px;
  gap: 8px;
}
#digital-menu-view #rest_topbar .logo-container {
  min-height: 30px;
}
#digital-menu-view #rest_topbar .logo-title-container {
  font-family: "Roboto", sans-serif;
  font-size: 140%;
  color: #222222;
}
#digital-menu-view .logo-container {
  background: url("images/logo.png") center center;
  background-repeat: no-repeat;
  background-size: contain;
  min-height: 120px;
  width: 100%;
  max-width: 350px;
}
#digital-menu-view .main-view-wrapper {
  position: relative;
  display: block;
}
#digital-menu-view .category-wrapper {
  display: flex;
  margin-top: 32px;
  justify-content: flex-end;
}
#digital-menu-view #digital-menu-category-list-view {
  display: flex;
}
#digital-menu-view .search-bar {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: transparent;
  box-sizing: border-box;
  padding: 8px;
  margin: 0 auto;
  max-width: 800px;
}
#digital-menu-view .search-bar input {
  position: relative;
  border-radius: 100px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  width: 100%;
  height: auto;
  color: #222222;
  font-family: "Roboto", sans-serif !important;
  font-size: 14px;
  font-weight: 400;
  background-color: transparent;
  border: 1px solid #222222;
  box-sizing: border-box;
  text-overflow: ellipsis;
  outline: none;
  -webkit-appearance: none;
}
#digital-menu-view .search-bar input:not(:placeholder-shown) ~ i {
  display: none;
}
#digital-menu-view .search-bar i {
  position: absolute;
  transform: translate(0, -50%);
  right: 16px;
  top: 50%;
}
#digital-menu-view #digital-menu-product-list-view {
  position: relative !important;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  padding: 0 !important;
  max-width: 800px;
  min-height: 250px;
  margin: 0 auto;
}
#digital-menu-view #digital-menu-product-list-view .spin-loader-container {
  position: absolute;
  display: block;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translatey(-50%);
}
#digital-menu-view #digital-menu-product-list-view .uitable {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  width: 100%;
  padding-bottom: 32px;
  padding-top: 8px;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  height: auto;
  padding: 8px;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .grid-row {
  display: grid;
  gap: 16px;
  grid-template-columns: auto 1fr auto;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .grid-col {
  display: grid;
  grid-template-rows: auto auto minmax(0, 1fr);
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .product-image-row-span {
  grid-row: 1/3;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .label.product-title {
  font-family: "Roboto", sans-serif;
  font-size: 2em;
  text-transform: lowercase;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .label.product-title:first-letter {
  text-transform: uppercase;
}
#digital-menu-view #digital-menu-product-list-view .uitable .cell .label.product-title .p-regular {
  line-height: 1.25;
}
#digital-menu-view #digital-menu-product-list-view .uitable .menu-section-title {
  padding: 12px 8px;
  margin-top: 15px;
  position: sticky;
  top: 10px;
  z-index: 10;
}
#digital-menu-view #digital-menu-product-list-view .uitable .product-image {
  height: 100px;
  width: 100px;
  border-radius: 16px;
  background-color: transparent;
}
#digital-menu-view #digital-menu-product-list-view .product-format-row-container {
  grid-column: 1/-1;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}
#digital-menu-view #digital-menu-product-list-view .product-format-row-container .product-format-line {
  display: grid;
  grid-template-columns: 1fr auto;
  padding: 4px 0;
  color: #545454;
}

#digital-menu-view.mobile #digital-menu-product-list-view .uitable .product-image {
  min-height: 70px;
  min-width: 70px;
}
#digital-menu-view.mobile #digital-menu-product-list-view .uitable .cell .label.product-title {
  font-family: "Roboto", sans-serif;
  text-transform: lowercase;
  text-transform: capitalize;
  font-size: 1.2em;
}
@media only screen and (max-width: 800px) {
  #digital-menu-view #digital-menu-product-list-view .product-table {
    grid-template-columns: 1fr;
  }
}
.nav-bar-wrapper {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 0;
  margin: 0;
  z-index: 10;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

#nav-bar {
  position: relative;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 8px;
  border: 0;
  padding: 8px;
  margin: 0 auto;
  max-width: 800px;
}
#nav-bar .logo-container {
  background-image: url(images/logo.png);
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;
  width: 200px;
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
  min-height: 100px;
}
#nav-bar .logo-container.reduce-logo {
  min-height: 40px;
}
#nav-bar .back-btn {
  display: block;
  position: relative;
  height: 28px;
  width: 28px;
  background: url(images/turn-back-blue.png) center center no-repeat;
  background-size: contain;
}

.hamburger {
  display: flex;
  align-items: center;
  position: relative;
  height: 26px;
  cursor: pointer;
}
.hamburger .main-nav-toggle {
  display: block;
  position: relative;
  width: 28px;
  height: 16px;
}
.hamburger .main-nav-toggle:after, .hamburger .main-nav-toggle:before {
  content: "";
  position: absolute;
  top: 0;
  height: 0;
  border-bottom: 4px solid #3e6c93;
  width: 100%;
  left: 0;
  right: 0;
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
}
.hamburger .main-nav-toggle:after {
  top: 100%;
}
.hamburger .main-nav-toggle i {
  display: block;
  text-indent: 100%;
  overflow: hidden;
  white-space: nowrap;
  height: 4px;
  background-color: #3e6c93;
  width: 100%;
  position: absolute;
  top: 50%;
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
}
.hamburger .main-nav-toggle.active-menu:after {
  transform: rotate(-45deg);
  transform-origin: center;
  top: 50%;
}
.hamburger .main-nav-toggle.active-menu:before {
  transform: rotate(45deg);
  transform-origin: center;
  top: 50%;
}
.hamburger .main-nav-toggle.active-menu i {
  opacity: 0;
}

#nav-window {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  height: 100dvh;
  max-width: 90vw;
  min-width: 35vw;
  padding: 8px;
  z-index: 20;
  background: linear-gradient(to right top, rgba(255, 255, 255, 0.82), rgba(255, 255, 255, 0.75));
  color: #3e6c93;
  transition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
  transform: translateX(-110%);
}
#nav-window.show-window {
  transform: translateX(0);
}
#nav-window ul {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 32px 8px;
}
#nav-window ul li {
  display: flex;
  flex-direction: row;
  gap: 8px;
  width: 100%;
  align-items: center;
  padding: 8px 32px;
}
#nav-window ul li:nth-child(odd) {
  background-color: rgba(62, 108, 147, 0.1);
}
#nav-window ul li:hover {
  filter: brightness(1.2);
  background-color: rgba(62, 108, 147, 0.2);
}
#nav-window .to-top-btn {
  position: absolute;
  bottom: 16px;
}

.bg-panel {
  padding: 16px;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(3.6px);
  -webkit-backdrop-filter: blur(5.6px);
  border: 1px solid rgba(255, 255, 255, 0.44);
  color: #212529;
}

.fade-in {
  animation: fadeIn ease-in-out 0.4s;
}
.fade-in.slow {
  animation-duration: 1.2s;
}
.fade-in.medium {
  animation-duration: 0.8s;
}
.fade-in.fast {
  animation-duration: 0.4s;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
#digital-menu-preset-selection-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0;
  margin: 0;
}
#digital-menu-preset-selection-view p {
  line-height: 1.2em;
}
#digital-menu-preset-selection-view h1 {
  line-height: 1.4em;
}
#digital-menu-preset-selection-view .sub-text {
  font-size: 70%;
  color: #9f9eac;
}
#digital-menu-preset-selection-view #nav-window .icon {
  width: 70px;
  height: 70px;
}
#digital-menu-preset-selection-view .bg-video-container {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  /* Style the button used to pause/play the video */
}
#digital-menu-preset-selection-view .bg-video-container #bg-video {
  position: fixed;
  right: 0;
  top: -200px;
  min-width: 100%;
  min-height: 100%;
}
#digital-menu-preset-selection-view .bg-video-container .screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.3) 90%);
}
#digital-menu-preset-selection-view .bg-video-container .content {
  position: fixed;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #f1f1f1;
  width: 100%;
  padding: 20px;
}
#digital-menu-preset-selection-view .bg-video-container #myBtn {
  width: 200px;
  font-size: 18px;
  padding: 10px;
  border: none;
  background: #000;
  color: #fff;
  cursor: pointer;
}
#digital-menu-preset-selection-view .bg-video-container #myBtn:hover {
  background: #ddd;
  color: black;
}
#digital-menu-preset-selection-view .content-container {
  display: flex;
  flex-direction: column;
  justify-items: center;
  gap: 16px;
  margin: 0 auto;
  margin-top: 150px;
  margin-bottom: 100px;
  padding: 0 8px;
  width: 100%;
  max-width: 800px;
}
#digital-menu-preset-selection-view .hero-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#digital-menu-preset-selection-view .hero-container h1 {
  font-size: 300%;
  font-weight: 700;
  color: #ffffff;
}
#digital-menu-preset-selection-view .hero-container p {
  font-size: 150%;
  color: #ffffff;
}
#digital-menu-preset-selection-view .section-info-container {
  position: relative;
  width: 100%;
  margin: 0 auto;
  height: auto;
}
#digital-menu-preset-selection-view .section-info-container h1 {
  font-size: 150%;
  font-weight: 700;
}
#digital-menu-preset-selection-view .venue-info-container {
  display: grid;
  grid-template-columns: 1fr 3fr;
  grid-template-rows: auto 1fr;
  gap: 16px;
}
#digital-menu-preset-selection-view .venue-info-container h1 {
  font-size: 150%;
  font-weight: 700;
}
#digital-menu-preset-selection-view .icon {
  transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1);
  cursor: pointer;
  color: #3e6c93;
  display: block;
  width: 150px;
  height: 150px;
  border-radius: 4px;
  background-color: white;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
#digital-menu-preset-selection-view .icon.lento {
  background-image: url(images/lento.jpg);
}
#digital-menu-preset-selection-view .icon.aptitude {
  background-image: url(images/aptitude.jpg);
}
#digital-menu-preset-selection-view .icon.derwandi {
  background-image: url(images/derwandi.jpg);
}
#digital-menu-preset-selection-view .icon.estkanahchai {
  background-image: url(images/estkanahchai.jpg);
}
#digital-menu-preset-selection-view .icon.lgymat {
  background-image: url(images/lgymat.jpg);
}
#digital-menu-preset-selection-view .icon.sugarcoat {
  background-image: url(images/sugarcoat.jpg);
}
#digital-menu-preset-selection-view .icon.wintertreats {
  background-image: url(images/wintertreats.jpg);
}
#digital-menu-preset-selection-view .icon.glaze {
  background-image: url(images/glaze.jpg);
}
#digital-menu-preset-selection-view .icon.treats {
  background-image: url(images/treats.jpg);
}
#digital-menu-preset-selection-view .icon.dual-link {
  background-image: url(images/dual-link.jpg);
}
#digital-menu-preset-selection-view .icon.pinocchio {
  background-image: url(images/pinocchio.jpg);
}
#digital-menu-preset-selection-view .icon.solysombra {
  background-image: url(images/solysombra.jpg);
}
#digital-menu-preset-selection-view .icon.laola {
  background-image: url(images/laola.jpg);
}
#digital-menu-preset-selection-view .icon:hover {
  filter: brightness(1.2);
  transform: scale(1.1);
}
#digital-menu-preset-selection-view footer {
  background-color: #ffffff;
  padding: 32px 8px;
}
#digital-menu-preset-selection-view footer .footer-content-container {
  display: flex;
  gap: 16px;
  margin: 0 auto;
  padding: 0 8px;
  width: 100%;
  max-width: 800px;
}
#digital-menu-preset-selection-view footer .footer-content-container .sponsor-logo {
  max-height: 60px;
}
#digital-menu-preset-selection-view .sub-footer {
  background-color: #F0F0F0;
}
#digital-menu-preset-selection-view .sub-footer .sub-footer-container {
  display: flex;
  gap: 16px;
  margin: 0 auto;
  padding: 8px;
  width: 100%;
  max-width: 800px;
  padding: 8px 0;
}
#digital-menu-preset-selection-view .sub-footer .sub-footer-container .copy-right {
  font-size: 70%;
  color: #888888;
}

@media (max-width: 800px) {
  /* … */
  #digital-menu-preset-selection-view .preset-grid {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 600px) {
  /* … */
  #digital-menu-preset-selection-view .preset-grid {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 360px) {
  /* … */
  #digital-menu-preset-selection-view .preset-grid {
    grid-template-columns: 1fr;
  }
}

/*# sourceMappingURL=app.css.map */
