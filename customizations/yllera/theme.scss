
// DECLARE NEW FONTS //////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////



.gfs-didot-regular {
  font-family: "GFS Didot", serif;
  font-weight: 400;
  font-style: normal;
}

.eb-garamond-light {
  font-family: "EB Garamond", serif;
  font-optical-sizing: auto;
  font-weight: 200;
  font-style: normal;
}

.eb-garamond-regular {
  font-family: "EB Garamond", serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

.eb-garamond-bold {
  font-family: "EB Garamond", serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
}




// VARIABLES //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


$brand-color-1: #CC3333;

$font-family: "EB Garamond", serif;
$font-family2: "GFS Didot", serif;

// #00BEBE (light)   (dark)
$action-color: $brand-color-1;
$action-color-dark: $brand-color-1;
$bg1: rgba(255,255,255,1); 
$bg2: rgba(0, 0, 0, 0.03);
$fg1: #4b3918 ;
$stripe-color: rgba(255,255,255,.2);
$curtain_color: $bg1;

$border-color: rgba(0, 0, 0, 0.1);
$text-primary-color: #616161; //#1A1A1A ;
$text-secondary-color: #AFAFBD ;
$text-inverted-color: #FFFFFF ;

$price-color: #77a464 ;



// BODY PROPERTIES ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

* {
  box-sizing: border-box;
}

html {
  //background-color:   // used for simulating bg of webpage iframe for testing
  box-sizing: border-box !important;

  background-color: $bg1;
  background: none;
  background-size: cover;
  min-height: 100%;

  font-family: $font-family;
  font-size: 16px !important;
  font-weight: normal;
  font-style: normal;
  color: $text-primary-color;
}

body {
  position: relative !important;
  display: block !important;
  height: auto !important;
  width: 100% !important;
  color: inherit !important;
  background: none !important;

  font-family: $font-family;
  font-size: 16px !important;
  font-weight: normal;
  font-style: normal;
  color: $text-primary-color;
}

a {
  color: $action-color !important;
  transition: all ease-in-out .15s;

  &:hover {
    filter: brightness(1.2);
  }
}

.bg-bg1 {
  background-color: transparent !important;
}

/// SCROLL BAR PROPERTIES ////////////// 

/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #aaa;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: #aaa;
}

*::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
  border: 3px solid #aaa;
}

.selected .checkbox-button::before, 
.checkbox-button.selected::before, 
.custom-selected 
.checkbox-button::before {
  border-bottom: 4px solid $action-color;
  border-right: 4px solid $action-color;

}

.flex-wrap {
  flex-wrap: wrap;
}

// GENERAL MODUALS ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////



.view,
.window {
    position: relative;
}

.oobtn {
    // transition: all ease-in-out .15s;
    transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);


    &.oobtn-primary {
        background-color: $action-color;
        border-radius: 4px;
        color: $text-inverted-color;

        background-image: linear-gradient(to right, $action-color, $action-color-dark);
    }

    &.oobtn-secondary {
        border: 1px solid $action-color;
        border-radius: 4px;
        color: $action-color;
        font-weight: bold;

        padding-left: 25px;
        padding-right: 25px;
    }

    &:hover {
        filter: brightness(1.2);
    }
}

.btn-minus,
.btn-plus,
.oobtn-icon {
    // transition: all ease-in-out .15s;
    transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
    cursor: pointer;
    color: $action-color;
    background: transparent !important;

    &:hover {
        filter: brightness(1.2);
    }
}


input,
textarea,
.combobox,
select {

    box-sizing: border-box !important;
    // font-family: 'gil-sans-light', sans-serif !important;
    // background: rgba(0, 0, 0, .1) !important;

    // FIX: campos no se alinean bien en producción, falta width de los input
    width: 100%;

    & option {
        // background: rgba(0, 0, 0, .9);
        // color: #fff;
        // text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
    }
}


#pickup-or-delivery .logo-container {
    background: url("images/logo.webp") center center;
    background-repeat: no-repeat;
    background-size: contain;

    min-height: 117px;
    width: 100%;
    max-width: 350px;

    // filter: drop-shadow(2px 4px 12px #000000);
}

.logo-title-container {
    font-family: $font-family2;
    font-size: 140%;
    color: $text-primary-color;
}


// prefered no background as default
#pickup-or-delivery,
#choose-menu-view,
#checkout-view,
#checkout-ok-view {
    background: transparent;
    overflow-y: auto;
    height: 100dvh;
    width: 100%;
    box-sizing: border-box !important;
}

.modal_window {
    color: $text-primary-color;
    border: 1px solid rgba(255, 255, 255, 0.33) !important;
    background-color: $bg1;
    box-shadow: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22) !important;
    border-radius: 8px;
}

// .footer-m {
//   background: linear-gradient(to right bottom, rgba(0,0,0,.4),rgba(0,0,0,.3)) !important;
// }

.footer-m-embed,
.footer-m {
    height: auto;
    padding: 8px;
}

.spin-loader {
    border: 3px solid $action-color !important;
    border-right-color: transparent !important;
    width: 32px;
    height: 32px;
}

.stepper .dot.selected {
    background-color: $text-inverted-color;
}

.stepper .dot.selected::before {
    border: 2px solid $text-inverted-color;
}

.selected .radio-button::before,
.custom-selected .radio-button::before {
    background-color: $action-color;
}

.button-close {
    &.cta {
        color: $action-color !important;
        background: transparent !important;
    }
}

input:focus,
textarea:focus {
    border-color: $action-color;
    outline-color: $action-color;
    outline: 0 !important;
}

.gap { gap: 8px; }

.wrap-col { flex-wrap: wrap; }

.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-full { grid-column: 1/-1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-5 { grid-row: span 5; }
.row-span-full {grid-row: 1/-1; }

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; } 
.justify-content-end { justify-content: flex-end; } 
.justify-content-space-between { justify-content: space-between; }
.justify-items-start { justify-items: flex-start; } 
.justify-items-center { justify-items: center; } 
.justify-items-end { justify-items: flex-end; } 
.justify-self-start { justify-self: flex-start; } 
.justify-self-center { justify-self: center; } 
.justify-self-end { justify-self: flex-end; } 
.align-content-start { align-content: flex-start; } 
.align-content-center { align-content: center; } 
.align-content-end { align-content: flex-end; } 
.align-items-start { align-items: flex-start; } 
.align-items-center { align-items: center; } 
.align-items-end { align-items: flex-end; } 
.align-self-start { align-self: flex-start; }  
.align-self-center { align-self: center; } 
.align-self-end { align-self: flex-end; }


// TABLE PROPERTIES ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#product-table-scroller {
  scroll-behavior: smooth;
}

.cell {
  border: none !important;
}

.hero {
  background: none;
  color: $text-primary-color;
}

.photo-tmpl .product-image {
  margin: 16px 16px 0 16px;
  border-radius: 16px !important;
  background-color: #ffffff;
  background-repeat: no-repeat;
  height: auto;
  min-height: 235px;
  background-size: 106% !important;

  border: 1px solid lightgray;
} 


.mobile .product-table,
.desktop .product-table { 

  display: grid !important;
  grid-template: auto / auto;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
  padding-left: 8px;
  padding-right: 8px;

  .section-header {
    background-color: $bg1;
    // border-bottom: 2px solid rgba(0, 0, 0, .2);
  }

  .menu-section-header,
  .menu-section-title {
    grid-column: 1 / -1 !important;
    box-sizing: border-box;

    font-family: $font-family2 !important;
    font-weight: 400;
    font-size: 1.6em;
    color: $text-primary-color;

    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    border-bottom: 3px solid $action-color;

      // background: none;
      // font-family: 'Great Vibes', cursive !important;
      // font-size: 220%;
      // text-align: center;
      // // border-top: 3px double #363636;
      // // border-bottom: 3px double #363636;
      // margin-top: 12px;
      // margin-bottom: 12px;
  }

  // .menu-section-title:before,
  // .menu-section-title:after {
  //     content: "";
  //     display: block;
  //     width: 100%;
  //     height: 20px;
  //     background: url( 'images/divider.svg' ) no-repeat center center;
  //     background-size: contain
  // }

  .cell {
    width: 100% !important;
    margin: 0 !important;
    background-color: rgba(255,255,255,.5);
    color: $text-primary-color;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);

    &>div {
        // border-radius: 1px solid $text-primary-color;
        // outline: 5px solid $bg2;
    }

    .hero {
      border-radius: 0 !important;
      margin-top: 0 !important;
      background-color: transparent !important;
      color: $text-primary-color;
    }
  
      // .image-cover {
      //     background-size: 105% auto !important;
      // }
      
      
  }

  
}

// make cells not rounded
.cell.rounded {
  // border-radius: 0 !important;
  // overflow: hidden;
}

.desktop #product-list-view > .product-table > .cell.product {
  // width: 233px !important;
  // min-width: 220px;
  // // flex: 1;
  // border-radius: 0px !important;
}




// PICKUP OR DELIVERY /////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


#pickup-or-delivery 
{
  .pickup-icon, 
  .take-away-icon,
  .table-icon {
    width: 150px;
    height: 150px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    border: 1px solid rgba(255,255,255,.6);
    border-radius: 50%;
    padding: 16px;
    background-size: 70% !important;
    background-color: rgba(0,0,0,.1);
  }

  .take-away-icon {
    background-image: url(images/take_away_dark.png) !important;
  }
  .pickup-icon {
    background-image: url(images/pickup_dark.png) !important;
  }
  .table-icon {
    background-image: url(images/table_dark.png) !important;
  }

  .take-away-block {}
  .pickup-block {}
  .table-block {}

  .hl-title-bold {
    font-family: $font-family2 !important;
    font-size: 120%;
    font-weight: bold;
  }

  .logo-container {
    background: url("images/logo.webp") center center;
    background-repeat: no-repeat;
    background-size: contain;

    min-height: 117px;
    width: 100%;
    max-width: 350px; 
  }
}



// CHOOSE MENU VIEW ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.desktop .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr) 360px;
  padding-left: 8px;
  padding-right: 8px;

  .ticket-view-wrapper {
    grid-row: 1/3;
    grid-column: 2/3;
  }
}

.mobile .menu-view-grid-container {
  display: grid;
  gap: 8px;
  grid-template-columns: minmax(0, 1fr);
  padding: 8px 8px 0 8px;
}


/////////////////// CATEGORY MENU VIEW //////////////////////////

#category-menu-view {
  position: relative !important;;

  font-size: 13px;
  letter-spacing: .05em;
  text-transform: uppercase;
  font-weight: 400;
  border-radius: 0;

  .glass-panel {
    border-radius: 2px !important;
  }

  .scroll-row {
    position: relative !important;
  }

  .menu-item {

    position: relative !important;

      &:hover,
      &.selected {

      }
  }

  .oobtn-link {
      color: $text-primary-color;
      font-weight: bold;
  }

  .oobtn-next, .oobtn-prev {
    position: relative !important;
    
    display: flex;
    align-items: center;
    
  }

    

  .oobtn-next:hover:before, .oobtn-prev:hover:before, .oobtn-link:hover {
      opacity: 1 !important;
      filter: brightness(1.2);
  }


  .btn-minus.disabled, .btn-plus.disabled, .oobtn-icon.disabled {
      color: #aaaaaa;
  }
}



/////////////////// PRODUCT LIST VIEW //////////////////////////

#product-add-view {
  height: inherit !important;
  max-height: 100vh;
}

.photo-tmpl.selected {
  border: 0 !important;

  &.glass-panel::after {
    background: linear-gradient(to right bottom, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.40)) !important; 
  }
}

.cell.rounded {
  border-radius: 2px;
}

.align-right-row {
  text-align: right;
  justify-content: flex-end;
}

.product-price {
  background-color: $action-color;
  color: $text-inverted-color;
  padding: 4px 8px;
  flex: inherit;
}

/////////////////// TICKET VIEW //////////////////////////

.desktop #ticket-view {
  width: 100%;
  position: relative !important;
  top: inherit !important;
}

#checkout-date-picker-view {
  height: auto !important;
}

.custom-stripe > .stripped:nth-child(2n),
.stripe > div:nth-child(2n) {
  background-color: $stripe-color;
}

.ticket-view-title {
  font-size: 150%;
  font-weight: bold;
  font-family: $font-family2 !important;
  color: $text-primary-color;
}


// CHECK OUT VIEW /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

#checkout-view.desktop .main-view-wrapper {
  padding-right: 8px;
}


#client-info-view,
#conditions-container,
#terms-wrapper {
    background-color: transparent !important;
    border-radius: 0px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    margin-top: 15px;
    color: $text-primary-color;

    .cell .hero {
        background-color: transparent !important;
        border-radius: 0 !important;
    }
}

.tab.selected {
  font-family: $font-family2 !important;
  border-color: $text-primary-color;
  color: $text-primary-color;
  font-size: 150%;
  font-weight: bold;
}

#terms-wrapper {
  margin-left: 10px !important;
  margin-right: 10px !important;
  background-color: rgba(0, 0, 0, 0.7);
  border: 0;
  margin-top: 16px;
}

#payment-modal-view {
  
}

#client-info-view {
  .oobtn-secondary {
    padding: 2px 4px;
  }
}

// DIGITAL MENU ///////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


// prefered no background as default
#digital-menu-view {

  background: transparent;
  overflow-y: auto;
  height: 100dvh;
  width: 100%;
  box-sizing: border-box !important;

  // &.root-screen {
  //   max-width: 1280px;
  //   margin: 0 auto;
  // }

  .scroll-row {
      position: relative !important;
      overflow: auto;
  }

  .menu-view-grid-container {
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
      gap: 0;
      padding: 0;
  }

  #rest_topbar {
      position: relative;
      background-color: $brand-color-1;
      border: 0;
      flex-direction: column;
      padding-top: 8px;
      gap: 8px;

      .logo-container {
          min-height: 30px;
      }

      .logo-title-container {
          font-family: $font-family2;
          font-size: 140%;
          color: $text-primary-color;
      }
  }

  .logo-container {
    background: url("images/logo.webp") center center;
    background-repeat: no-repeat;
    background-size: contain;

    min-height: 117px;
    width: 100%;
    max-width: 350px;

    // filter: drop-shadow(2px 4px 12px #000000);
  }

  .main-view-wrapper {
      position: relative;
      display: block;

      // must not be overflow:auto for stick to work, leave as default
      overflow: initial;
    }

  .category-wrapper {
    display: flex;
    margin-top: 32px;
    justify-content: flex-end;
    overflow: auto; //necessary to scroll category list in side window
  }

  #digital-menu-category-list-view {
    display: flex;

    .menu-item:hover {
       backdrop-filter: brightness(140%);
    }
  }

  .search-bar {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: transparent;
    box-sizing: border-box;
    padding: 8px;
    margin: 0 auto;
    max-width: 800px;

      input {
          position: relative;
          border-radius: 100px;
          -webkit-border-radius: 100px;
          -moz-border-radius: 100px;
          width: 100%;
          height: auto;
          // background-color: transparent;
          color: $text-primary-color;
          font-family: $font-family !important;
          font-size: 16px;
          font-weight: 400;
          background-color: transparent;
          border: 1px solid $action-color;
          box-sizing: border-box;
          text-overflow: ellipsis;
          outline: none;
          -webkit-appearance: none;

          // &:before {
          //     content: "\e8bd";
          //     /* this is your text. You can also use UTF-8 character codes as I do here */
          //     font-family: 'feather';
          //     left: -5px;
          //     position: absolute;
          //     top: 0;
          // }

          &:focus { border-color: #ffffff; }
          &:not(:placeholder-shown) ~ i { display: none; }
      }

      i {
          position: absolute;
          transform: translate(0, -50%);
          right: 16px;
          top: 50%;
      }
  }

  #digital-menu-product-list-view {
      position: relative !important;
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 100%;
      padding: 0 !important;

      max-width: 800px;
      min-height: 250px;
      margin: 0 auto;

      .spin-loader-container {
          position: absolute;
          display: block;
          top: 50%;
          left: 50%;
          transform: translateX(-50%) translatey(-50%);
      }

      .uitable {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          width: 100%;
          //give some space after last cell
          padding-bottom: 32px;
          padding-top: 8px;

          .menu-section-title {
              padding: 12px 8px;
              margin-top: 15px;

              //CAREFUL: sticky title properties, some parent clases much not have overflow auto
              position: sticky;
              top: 10px;
              z-index: 10;
          }

          .cell.product {
              position: relative;
              display: flex;
              flex-direction: row;
              gap: 16px;
              height: auto;
              padding: 8px;

              .product-image {
                  height: 100px;
                  width: 100px;
                  border-radius: 16px;
                  background-color: #ffffff;
                  background-position: center center;
                  background-repeat: no-repeat;
                  background-size: cover;
                  align-self: center;
              }

              .product-cell-info-container {
                  display: flex;
                  flex-direction: column;
                  flex: 1;
                  gap: 8px;

                  .row {
                      display: flex;
                      justify-content: space-between;
                      gap: 8px;
                      align-items: flex-end;

                      .product-title {
                          font-family: $font-family;
                          font-weight: 600;
                          font-size: 1.4em;
                          letter-spacing: -.05em;
                          text-transform: lowercase;
                          &:first-letter {text-transform: uppercase;}

                          .p-regular {
                              letter-spacing: -.05em;
                              line-height: 1.25;
                          }
                      }
                      
                      .product-price {}
                  }

                  .product-format-row-container {
                      grid-column: 1 / -1;
                      border-top: 1px solid $bg2;
                      border-bottom: 1px solid $bg2;
          
                      .product-format-line {
                          display: grid;
                          grid-template-columns: 1fr auto;
                          padding: 4px 0;
                          color: $text-secondary-color;
                      }
                  }

                  .product-desc {
                      letter-spacing: -.05em;
                      line-height: 1.25;
                  }

                  .product-allergies {}

              }
          }
      }
  }
}

#digital-menu-view.mobile {

  #digital-menu-product-list-view .uitable .menu-section-title {}

  #digital-menu-product-list-view .uitable .cell.product {}

  #digital-menu-product-list-view .uitable .cell.product .product-image {
      height: 70px;
      width: 70px;
      max-width: 70px;
      width: 100%;
  }

  #digital-menu-product-list-view .uitable .cell.product .product-cell-info-container .row .product-title {
      font-size: 1.2em;
  }

  #digital-menu-product-list-view .uitable .cell.product .product-desc {}
  #digital-menu-product-list-view .uitable .cell.product .product-price {}

}



@media only screen and (max-width: 800px) {

 #checkout-view #terms-wrapper .change-row{
  flex-direction: column !important;
 }

  #digital-menu-view #digital-menu-product-list-view .product-table {
      grid-template-columns: 1fr;
  }

}


.nav-bar-wrapper {
  position: relative;
  width: 100%;
  padding: 0;
  margin: 0;

  background-color: $bg1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

#nav-bar {
  position: relative;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 8px;
  border: 0;
  padding: 8px 0;
  margin: 0 auto;
  max-width: 800px;

  .logo-container {
      min-height: 117px;
      background-position: center center;
      transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);

      &.reduce-logo {
          min-height: 40px;
      }
  }  

}

//styling for hamburger button
.hamburger {
  display: flex;
  align-items: center;
  position: relative;
  height: 26px;
  padding: 12px;
  cursor: pointer;

  .main-nav-toggle {
      display: block;
      position: relative;
      width: 28px;
      height: 16px;

      &:after,
      &:before {
          content: '';
          position: absolute;
          top: 0;
          height: 0;
          border-bottom: 4px solid $action-color;
          width: 100%;
          left: 0;
          right: 0;
          // transition: all ease-out 0.3s;
          transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
      }

      &:after {
          top: 100%;
      }

      i {
          display: block;
          text-indent: 100%;
          overflow: hidden;
          white-space: nowrap;
          height: 4px;
          background-color: $action-color;
          width: 100%;
          position: absolute;
          top: 50%;
          // transition: all ease-out 0.1s;
          transition: all 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
      }

      &.active-menu {
          &:after {
              transform: rotate(-45deg);
              transform-origin: center;
              top: 50%;
          }

          &:before {
              transform: rotate(45deg);
              transform-origin: center;
              top: 50%
          }

          i {
              opacity: 0;
          }
      }
  }
}

#nav-window {
  position: absolute;
  top: 0;
  display: flex;
  flex-direction: column;
  height: 100dvh;
  max-width: 90vw;
  min-width: 35vw;
  padding: 8px;
  z-index: 20;
  
  background-color: $bg1;
  color: $text-primary-color;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

  transition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
  transform: translateX(-110%);

  &.show-window {
      transform: translateX(0);
  }

  .button-wrapper {
      min-height: 50px;
      display: flex;
      justify-content: flex-end;
  }

}


// STYLE CLASSES //////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////


.glass-panel {

  /* From https://css.glass */
  // background: rgba(255, 255, 255, 0.15) !important;
  position: relative;
  background: none !important;
  // background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
  // border-radius: .8em !important;
  // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
  // backdrop-filter: blur(5.3px) !important;
  // -webkit-backdrop-filter: blur(5.3px) !important;
  // border: 1px solid rgba(255, 255, 255, 0.33) !important;

  color: $text-primary-color !important;

  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;

    background-color: $bg1;
    background: linear-gradient(to right bottom, rgba(255,255,255,.3),rgba(255,255,255,.15)) !important;
    border-radius: 3px !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(5.3px) !important;
    -webkit-backdrop-filter: blur(5.3px) !important;
    border: 1px solid $border-color !important;
  }
}

// ANIMATIONS /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

.fade-in {
  animation: fadeIn ease-in-out .4s;

  &.slow { animation-duration: 1.2s; }
  &.medium { animation-duration: .8s; }
  &.fast { animation-duration: .4s; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}







//////////// SORT THIS OUT /////////////////////////////////////////

.tmpl-img #choose-menu-view.mobile .main-view-wrapper {
    margin: 4px !important;
}


.desktop .photo-tmpl {
    // box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
    transition: all 0.3s cubic-bezier(.25,.8,.25,1) !important;
}

.desktop .photo-tmpl:hover {
    will-change: inherit !important;
    -webkit-transform: none !important;
    transform: none !important;
    -webkit-animation-timing-function: inherit !important;
    animation-timing-function: inherit !important;

    // box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23) !important;
}



.ui-navigation-controller > div {
  position: relative !important;
  background: none !important;
}












