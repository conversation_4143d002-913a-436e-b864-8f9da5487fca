/**
 * Created by godshadow on 30/11/2016.
 */

const kPaymentURL = "https://payment.dual-link.com/";

const kAPIURL = "https://v62-staging-hq.dual-link.com/api/";
const kSyncURL = "https://v62-staging-hq.dual-link.com/sync/";


enum APP_TYPE {
  DELIVERY = "delivery",
  BOOKING = "booking",
  TAKEAWAY_OR_DELIVERY = "takeaway_or_delivery",
  MENU = "menu",
  TAKEAWAY = "takeaway",
  RETAIL = "retail",
  PAYMENT = "payment",
  TABLE_ORDER = "table_order"
}


class AppDelegate extends MIOObject
{    
    options:any = {};
    appType:APP_TYPE;
    dbID:string = null;
    window:MUIWindow = null;
    config:any = {};
    //deliveryRouteId: string = "" ;

    didFinishLaunching (options) {

        DOWLAppHelper.sharedInstance().saveUrlOptions(options);

        //this.dbID = DOWLAppHelper.sharedInstance().supportedDBIDByIndex(options.dbid);        
        let data = MIOBundle.mainBundle().pathForResourceOfType("app", "plist");
        this.config = MIOPropertyListSerialization.propertyListWithData(data, 0, 0, null);
        
        let urls = this.config["ServerURLs"] || {} ;

        DLOrderWebLib.sharedInstance().apiURL     = urls["APIURL"    ] || kAPIURL;
        DLOrderWebLib.sharedInstance().syncURL    = urls["SyncURL"   ] || kSyncURL;
        DLOrderWebLib.sharedInstance().paymentURL = urls["PaymentURL"] || kPaymentURL;
        DLOrderWebLib.sharedInstance().schemaID = this.config["OnlineSchemeID"];

        // NOTE: Force to load localized String
        let k = MIOLocalizeString("OK", "OK");

        this.options = options;

        // TODO: is_uuid   <----------WHAT DOES THIS MEAN?

        // DECLARE APP TYPE: if not url option variable is given, default app type is TAKEAWAY_OR_DELIVERY
        this.appType = this.options.qr ? APP_TYPE.TABLE_ORDER : APP_TYPE.TAKEAWAY_OR_DELIVERY;        

        // SET APP TYPE: if url option "type" exists, check against APP_TYPE ENUM and set appType
        if (this.options.type)
          for (const possibleType in APP_TYPE)
              if (APP_TYPE[ possibleType ] === this.options.type) {
                   this.appType = this.options.type as APP_TYPE;
                   break ;
                }
        

        let rvc = this.rootViewController();
        if (rvc instanceof MUINavigationController) AppScreen.sharedInstance().navigationController = rvc;

        this.window = new MUIWindow();
        this.window.initWithRootViewController(rvc);

        // Kludge: themes are becoming so complex that for some decissions we have to adjust de CSS with the "theme"
        const theme = DOWLAppHelper.sharedInstance().layoutTemplate ,
        theme_classname   = theme === LAYOUT_TEMPLATE.IMAGE_BASED  ? "tmpl-img"      :
                            theme === LAYOUT_TEMPLATE.STANDARD     ? "tmpl-standard" : 
                            theme === LAYOUT_TEMPLATE.RETAIL_BASED ? "tmpl-retail"   :
                            theme === LAYOUT_TEMPLATE.TEXT_BASED   ? "tmpl-img"      : "" ;

        if (theme_classname !== "")
             MUICoreLayerAddStyle(document.body, theme_classname);

        // if ( theme === LAYOUT_TEMPLATE.RETAIL_BASED )
        //   {
        //      ProductCart.instance.order.type = OnlineOrderType.Delivery ;
        //      ProductCart.instance.order.clientPostalCode = "03015" ;
        //   }
        // const old_resize = window.onresize ;
        // window.onresize = function() { document.body.height = window.innerHeight; (old_resize && old_resize( )) ; }
        // window.onresize( null ); 
    }

    rootViewController() {

        switch (this.appType) {

          case APP_TYPE.RETAIL:
              {
                DOWLAppHelper.sharedInstance().serviceType = ONLINE_SERVICE.DELIVERY ;
                const root = AppScreen.create( SCREEN.MENU ) ;

                if (MIOCoreIsPhone() === true) {
                    //  return root ;
                    let nc = new MUINavigationController();
                    nc.initWithRootViewController(root) ;
                } else {
                    let nc = new MUINavigationController();
                    nc.initWithRootViewController(root) ;
                    return nc ;
                }
              }


          // call like: http://localhost:9090/app/?type=payment&entityName=OnlineOrder&entityID=123123-123-123123-123
          case APP_TYPE.PAYMENT:
               DOWLAppHelper.sharedInstance().serviceType = ONLINE_SERVICE.PAYMENT ;
               return AppScreen.create( SCREEN.PAYMENT, 
                                        { entityID: this.options[ "entity-id" ],
                                          entityName: this.options[ "entity-name" ],
                                          notifySchema: this.options[ "notify-schema" ],
                                          embbeded: this.options[ "embbeded" ] === "true" });


          case APP_TYPE.MENU:
               DOWLAppHelper.sharedInstance().serviceType = ONLINE_SERVICE.DIGITAL_MENU;
               return AppScreen.create( SCREEN.DIGITAL_MENU );


          case APP_TYPE.BOOKING:
              {
               const root = AppScreen.create( SCREEN.BOOKING_CALENDAR_FORM );
               const nc = new MUINavigationController();
               nc.initWithRootViewController(root) ;
               return nc ;
              }


          case APP_TYPE.DELIVERY:
          case APP_TYPE.TAKEAWAY:


          case APP_TYPE.TABLE_ORDER:
              {
                  DOWLAppHelper.sharedInstance().serviceType =
                      this.appType === APP_TYPE.DELIVERY    ? ONLINE_SERVICE.DELIVERY :
                      this.appType === APP_TYPE.TABLE_ORDER ? ONLINE_SERVICE.TABLE_ORDER : ONLINE_SERVICE.TAKEAWAY ;

                  const product_cart = ProductCart.instance, 
                        order = product_cart.order ;

                  product_cart.canEditAddress = false ;

                  order.type = this.appType === APP_TYPE.DELIVERY    ? OnlineOrderType.Delivery : 
                               this.appType === APP_TYPE.TABLE_ORDER ? OnlineOrderType.atVenue : OnlineOrderType.TakeAway ;
                  
                  if ( order.type === OnlineOrderType.atVenue )
                      order.qrCode = this.options.qr ;

                  order.sessionDeliveryIsNextDay = false ;

                  if ( order.type === OnlineOrderType.TakeAway || order.type === OnlineOrderType.atVenue ) {
                      order.routeID     = null ;
                      order.routeName   = "" ;
                      order.deliveryFee = 0 ;
                    } else {
                      order.clientAddress    = this.options[ "address"      ] ;
                      order.clientAddress2   = this.options[ "address2"     ] ;
                      order.clientPostalCode = this.options[ "zip_code"     ] ;
                      order.routeID          = this.options[ "route_id"     ] ;
                      order.routeName        = this.options[ "route_name"   ] ;
                      order.deliveryFee      = this.options[ "delivery_fee" ] ;
                      product_cart.minAmount = this.options[ "min_delivery_amount" ] ;
                    }

                  const root = AppScreen.create( SCREEN.MENU ) ;

                    if ( MIOCoreIsPhone() === true ) {
                      //  return root ;
                      let nc = new MUINavigationController();
                      nc.initWithRootViewController(root) ;
                      return nc ;

                    } else {
                      let nc = new MUINavigationController();
                      nc.initWithRootViewController(root) ;
                      return nc ;
                  }              
              }


          case APP_TYPE.TAKEAWAY_OR_DELIVERY:
              {
                  const root = AppScreen.create(SCREEN.PICKUP_OR_DELIVER);

                  if ( MIOCoreIsPhone() === true ){
                    //  return root ;
                    let nc = new MUINavigationController();
                    nc.initWithRootViewController(root);
                    return nc ;

                  } else {
                    let nc = new MUINavigationController();
                    nc.initWithRootViewController(root);
                    return nc ;
                }              
              }
          }
      }


}
